
#include "plugin.format.xml.h"

WD::WDExtension* Create(WD::WDCore& app)
{
    return new WD::PluginFormatXML(app);
}

void Destroy(WD::WDExtension* extension)
{
    delete extension;
}

WD_EXTENSION_EXPORT void GetExtensionInfor(WD::WDExtensionInfor& infor)
{
    infor.anthor            =   "pr.cd";
    infor.name              =   WD::PluginFormatXML::Name;
    infor.type              =   WD::PluginFormatXML::ExtensionType;
    infor.createFunction    =   Create;
    infor.destroyFunction   =   Destroy;
}
