#include "CustomAttributeWidget.h"
#include "WDTranslate.h"

#include "QtPropertyBrowser/qtpropertymanager.h"
#include "QtPropertyBrowser/qtvariantproperty.h"
#include "QtPropertyBrowser/qteditorfactory.h"
#include "QtPropertyBrowser/qttreepropertybrowser.h"

#include "Private/TypeAttributeSimple.h"
#include "Private/TypeAttributeVec3.h"
#include "Private/TypeAttributeVec2.h"
#include "Private/TypeAttributeQuat.h"
#include "Private/TypeAttributeElement.h"
#include "Private/TypeAttributeStringVector.h"
#include "Private/TypeAttributeIntVector.h"
#include "Private/TypeAttributeDoubleVector.h"
#include "Private/TypeAttributeElementVector.h"
#include "Private/TypeAttributeEnum.h"

#include <QTreeWidgetItem>
#include "QtPropertyBrowser/qtpropertybrowser.h"
#include "core/WDCore.h"
#include "core/nodeTree/WDNodeTree.h"
#include <QHBoxLayout>
#include <QPushButton>
#include <QLineEdit>
#include <QMessageBox>
#include "core/businessModule/typeMgr/WDBMAttrEnumDictionary.h"
#include "core/message/WDMessage.h"
#include "undoRedo/WDUndoStack.h"

CustomAttributeWidget::CustomAttributeWidget(QObject *parent)
    : AttributeWidgetBase(parent)
{
    _flags = F_None;

    initCreatePropertyMgrs();
}
CustomAttributeWidget::CustomAttributeWidget(const Flags& flags, QObject* parent)
    : AttributeWidgetBase(parent)
{
    _flags = flags;

    initCreatePropertyMgrs();
}
CustomAttributeWidget::~CustomAttributeWidget()
{
    this->clearAttrTypes();

    uninitDestroyPropertyMgrs();
}

bool CustomAttributeWidget::initWidget(const WD::WDBMTypeDesc::AttrDescs& attrDescs)
{
    // 清除界面
    this->clearAttrTypes();
    // 用于属性分组
    std::map<std::string, QtProperty*> groupPtyMap;

    TypeAttributeSimple::FuncNameTs funcNameTS = std::bind(&CustomAttributeWidget::nameTS
        , this
        , std::placeholders::_1
        , std::placeholders::_2
        , std::placeholders::_3);

    for (const auto& pAttrDesc : attrDescs)
    {
        if (pAttrDesc == nullptr)
            continue;
        auto& attrDesc = *pAttrDesc;
        // 跳过无名称的属性
        if (!attrDesc.valid())
            continue;
        // 跳过隐藏属性
        if (attrDesc.flags().hasFlag(WD::WDBMAttrDesc::F_Hidden))
            continue;
        // 判断是否可编辑
        bool editable = !attrDesc.flags().hasFlag(WD::WDBMAttrDesc::F_ReadOnly);

        TypeAttributeBase* pAttrBaseType = nullptr;
        // 枚举字典，如果有枚举字典，则使用枚举字典方式更新界面
        const auto pEnumDict = attrDesc.enumDict();
        if (pEnumDict != nullptr) 
        {
            auto pMgr = editable ? _pEnumMgr : _pEnumMgrOnlyRead;
            // 如果可编辑则使用绑定factory的mgr
            pAttrBaseType = new TypeAttributeEnum(*this, attrDesc, pMgr, funcNameTS);
        }
        else
        {
            switch (attrDesc.type())
            {
            case WD::WDBMAttrValueType::T_Bool:
            {
                auto pMgr = editable ? _pVariantMgr : _pVariantMgrOnlyRead;
                pAttrBaseType = new TypeAttributeSimple(*this, attrDesc, pMgr, funcNameTS);
            }
            break;
            case WD::WDBMAttrValueType::T_Int:
            {
                auto pMgr = editable ? _pIntMgr : _pIntMgrOnlyRead;
                pAttrBaseType = new TypeAttributeSimple(*this, attrDesc, pMgr, funcNameTS);
            }
            break;
            case WD::WDBMAttrValueType::T_Double:
            {
                auto pMgr = editable ? _pDoubleMgr : _pDoubleMgrOnlyRead;
                pAttrBaseType = new TypeAttributeSimple(*this, attrDesc, pMgr, funcNameTS);
            }
            break;
            case WD::WDBMAttrValueType::T_String:
            case WD::WDBMAttrValueType::T_Uuid:
            case WD::WDBMAttrValueType::T_Word:
            {
                auto pMgr = editable ? _pStringMgr : _pStringMgrOnlyRead;
                pAttrBaseType = new TypeAttributeSimple(*this, attrDesc, pMgr, funcNameTS);
            }
            break;
            case WD::WDBMAttrValueType::T_GeometryRef:
            {
                // 几何引用类型目前不支持修改
                pAttrBaseType = new TypeAttributeSimple(*this, attrDesc, _pStringMgrOnlyRead, funcNameTS);
            }
            break;
            case WD::WDBMAttrValueType::T_Color:
            {
                auto pMgr = editable ? _pColorMgr : _pColorMgrOnlyRead;
                pAttrBaseType = new TypeAttributeSimple(*this, attrDesc, pMgr, funcNameTS);
            }
            break;
            case WD::WDBMAttrValueType::T_DVec3:
            {
                auto pStringMgr = editable ? _pStringMgr : _pStringMgrOnlyRead;
                auto pDoubleMgr = editable ? _pDoubleMgr : _pDoubleMgrOnlyRead;

                pAttrBaseType = new TypeAttributeVec3(*this, attrDesc, pStringMgr, pDoubleMgr, funcNameTS);
            }
            break;
            case WD::WDBMAttrValueType::T_DVec2:
            {
                auto pStringMgr = editable ? _pStringMgr : _pStringMgrOnlyRead;
                auto pDoubleMgr = editable ? _pDoubleMgr : _pDoubleMgrOnlyRead;

                pAttrBaseType = new TypeAttributeVec2(*this, attrDesc, pStringMgr, pDoubleMgr, funcNameTS);
            }
            break;
            case WD::WDBMAttrValueType::T_DQuat:
            {
                auto pStringMgr = editable ? _pStringMgr : _pStringMgrOnlyRead;
                auto pDoubleMgr = editable ? _pDoubleMgr : _pDoubleMgrOnlyRead;

                pAttrBaseType = new TypeAttributeQuat(*this, attrDesc, pStringMgr, pDoubleMgr, funcNameTS);
            }
            break;
            case WD::WDBMAttrValueType::T_NodeRef:
            {
                auto pMgr = editable ? 
                    (_flags.hasFlag(F_AddCEButtonToNodeRef) ? _pMyLineEditStringMgr: _pStringMgr) 
                    : _pStringMgrOnlyRead;

                pAttrBaseType = new TypeAttributeElement(*this, attrDesc, pMgr, funcNameTS);
            }
            break;
            case WD::WDBMAttrValueType::T_NodeRefs:
            {
                auto pMgr = editable ? _pStringMgr : _pStringMgrOnlyRead;
                pAttrBaseType = new TypeAttributeElementVector(*this, attrDesc, _pStringMgrOnlyRead, pMgr, funcNameTS);
            }
            break;
            case WD::WDBMAttrValueType::T_IntVector:
            case WD::WDBMAttrValueType::T_LevelRange:
            {
                auto pStringMgr = editable ? _pStringMgr : _pStringMgrOnlyRead;
                auto pIntMgr = editable ? _pIntMgr : _pIntMgrOnlyRead;
                pAttrBaseType = new TypeAttributeIntVector(*this, attrDesc, pStringMgr, pIntMgr, funcNameTS);
            }
            break;
            case WD::WDBMAttrValueType::T_DoubleVector:
            {
                auto pStringMgr = editable ? _pStringMgr : _pStringMgrOnlyRead;
                auto pDoubleMgr = editable ? _pDoubleMgr : _pDoubleMgrOnlyRead;
                pAttrBaseType = new TypeAttributeDoubleVector(*this, attrDesc, pStringMgr, pDoubleMgr, funcNameTS);
            }
            break;
            case WD::WDBMAttrValueType::T_StringVector:
            {
                auto pMgr = editable ? _pStringMgr : _pStringMgrOnlyRead;
                pAttrBaseType = new TypeAttributeStringVector(*this, attrDesc, pMgr, funcNameTS);
            }
            break;
            }
        }

        if (pAttrBaseType == nullptr)
        {
            assert(false && "属性界面初始化失败!");
            continue;
        }
        auto pRootProperty = pAttrBaseType->rootProperty();
        if (pRootProperty == nullptr)
        {
            delete pAttrBaseType;
            pAttrBaseType = nullptr;

            assert(false && "属性界面初始化失败!");
            continue;
        }
        // 存储
        _typeAttrBaseMap[attrDesc.name()] = pAttrBaseType;
        // 判断属性是否有分组，如果有分组，需要建立分组并将属性挂载到对应的分组下
        const auto& cate = attrDesc.category();
        bool bCate = false;
        if (!cate.empty())
        {
            auto strs = WD::StringSplit(cate, "/");

            std::string tName = "";
            tName.reserve(100);

            QtProperty* pParPty = nullptr;
            for (const auto& str : strs)
            {
                if (str.empty())
                    continue;
                if (!tName.empty())
                    tName += "/";
                tName += str;

                QtProperty* pTPty = nullptr;
                auto fItr = groupPtyMap.find(tName);
                if (fItr != groupPtyMap.end())
                {
                    pTPty = fItr->second;
                }
                else
                {
                    // 这里后续需要考虑给分组做翻译
                    pTPty = _pGroupMgr->addProperty(QString::fromUtf8(str.c_str()));
                    groupPtyMap[tName] = pTPty;
                }
                if (pTPty == nullptr)
                    continue;

                if (pParPty != nullptr)
                    pParPty->addSubProperty(pTPty);
                else
                    _pWidget->addProperty(pTPty);

                pParPty = pTPty;
            }
            if (pParPty != nullptr)
            {
                pParPty->addSubProperty(pRootProperty);
                bCate = true;
            }
        }
        // 没有处理到分组,直接加到根
        if (!bCate)
        {
            _pWidget->addProperty(pRootProperty);
        }
        //设置默认折叠
        auto items = _pWidget->items(pRootProperty);
        if (!items.empty())
            _pWidget->setExpanded(items.front(),false);
    }

    return true;
}

void CustomAttributeWidget::updateDisplayValue(const std::vector<std::pair<std::string, WD::WDBMAttrValue> >& values)
{
    if (values.empty())
        return;

    std::map<std::string, const WD::WDBMAttrValue*> tmpMap;
    for (const auto& v : values) 
    {
        tmpMap[v.first] = &(v.second);
    }

    for (auto& itr : _typeAttrBaseMap)
    {
        auto pTypeAttrBase = itr.second;
        if (pTypeAttrBase == nullptr)
        {
            assert(false);
            continue;
        }
        auto fItr = tmpMap.find(pTypeAttrBase->getAttrDesc().name());
        if (fItr != tmpMap.end())
        {
            pTypeAttrBase->setCurrentValue(*(fItr->second));
        }
    }
}

QWidget * CustomAttributeWidget::getWidget()
{
    // 翻译表头
    std::string trKey = _nameTSFunc ? _nameTSFunc("key", NameTsType::NTT_Others, "") : "key";
    std::string trValue = _nameTSFunc ? _nameTSFunc("value", NameTsType::NTT_Others, "") : "value";
    _pWidget->setLabelText(QString::fromUtf8(trKey.c_str())
        , QString::fromUtf8(trValue.c_str()));

    return _pWidget;
}

WD::WDBMAttrValue CustomAttributeWidget::getValue(const std::string_view& name) const
{
    auto pBase = findTypeAttrBase(name);
    if (pBase == nullptr)
        return WD::WDBMAttrValue();
    return pBase->getCurrentValue();
}
std::vector<std::pair<const WD::WDBMAttrDesc*, WD::WDBMAttrValue> > CustomAttributeWidget::getValues() const
{
    std::vector<std::pair<const WD::WDBMAttrDesc*, WD::WDBMAttrValue> > rValues;
    rValues.reserve(_typeAttrBaseMap.size());
    for (auto& itr : _typeAttrBaseMap)
    {
        if (itr.second == nullptr)
            continue;
        const auto* pDesc = &(itr.second->getAttrDesc());
        auto value = itr.second->getCurrentValue();
        rValues.push_back({ pDesc, value});
    }
    return rValues;
}

void CustomAttributeWidget::slotVariantValueChanged(QtProperty * p, const QVariant& val)
{
    WDUnused(val);

    if (!_valueChangedFunc)
        return;

    TypeAttributeBase* pAttrBaseType = this->findTypeAttrBase(p);
    if (pAttrBaseType == nullptr)
        return ;
    if(!pAttrBaseType->updateShow(p))
        return ;
    auto rValue = pAttrBaseType->getCurrentValue();
    if (!rValue.valid())
        return;
    _valueChangedFunc(rValue, pAttrBaseType->getAttrDesc());
}

void CustomAttributeWidget::slotDoubleValueChanged(QtProperty* p, double val)
{
    WDUnused(val);

    if (!_valueChangedFunc)
        return;

    TypeAttributeBase* pAttrBaseType = this->findTypeAttrBase(p);
    if (pAttrBaseType == nullptr)
        return ;
    if(!pAttrBaseType->updateShow(p))
        return ;
    auto rValue = pAttrBaseType->getCurrentValue();
    if (!rValue.valid())
        return;
    _valueChangedFunc(rValue, pAttrBaseType->getAttrDesc());
}
void CustomAttributeWidget::slotStringValueChanged(QtProperty* p, const QString& val)
{
    WDUnused(val);

    if (!_valueChangedFunc)
        return;

    TypeAttributeBase* pAttrBaseType = this->findTypeAttrBase(p);
    if (pAttrBaseType == nullptr)
        return ;
    if(!pAttrBaseType->updateShow(p))
        return ;
    auto rValue = pAttrBaseType->getCurrentValue();
    if (!rValue.valid())
        return;
    _valueChangedFunc(rValue, pAttrBaseType->getAttrDesc());
}

void CustomAttributeWidget::slotNodeRefCEClicked(QtProperty* p)
{
    TypeAttributeBase* pAttrBaseType = this->findTypeAttrBase(p);
    if (pAttrBaseType == nullptr)
        return;
    if (!_clickedCEBtnFunction)
        return ;
    auto pRNode = _clickedCEBtnFunction(pAttrBaseType->getAttrDesc());
    QObject* sender = this->sender();
    QtStringPropertyManager* propertyMgr = dynamic_cast<QtStringPropertyManager*>(sender);
    if (pRNode != nullptr && propertyMgr != nullptr)
    {
        propertyMgr->setValue(p, QString::fromUtf8(pRNode->name().c_str()));
    }
}
void CustomAttributeWidget::slotIntValueChanged(QtProperty* p, int val)
{
    WDUnused(val);

    if (!_valueChangedFunc)
        return;

    TypeAttributeBase* pAttrBaseType = this->findTypeAttrBase(p);
    if (pAttrBaseType == nullptr)
        return ;
    if(!pAttrBaseType->updateShow(p))
        return ;
    auto rValue = pAttrBaseType->getCurrentValue();
    if (!rValue.valid())
        return ;
    _valueChangedFunc(rValue, pAttrBaseType->getAttrDesc());
}
void CustomAttributeWidget::slotColorValueChanged(QtProperty* p, const QColor& val)
{
    WDUnused(val);

    if (!_valueChangedFunc)
        return;

    TypeAttributeBase* pAttrBaseType = this->findTypeAttrBase(p);
    if (pAttrBaseType == nullptr)
        return ;
    if(!pAttrBaseType->updateShow(p))
        return ;
    auto rValue = pAttrBaseType->getCurrentValue();
    if (!rValue.valid())
        return ;
    _valueChangedFunc(rValue, pAttrBaseType->getAttrDesc());
}
void CustomAttributeWidget::slotEnumValueChanged(QtProperty* p, int val)
{
    WDUnused(val);

    if (!_valueChangedFunc)
        return;

    TypeAttributeBase* pAttrBaseType = this->findTypeAttrBase(p);
    if (pAttrBaseType == nullptr)
        return ;
    if(!pAttrBaseType->updateShow(p))
        return ;
    auto rValue = pAttrBaseType->getCurrentValue();
    if (!rValue.valid())
        return ;
    _valueChangedFunc(rValue, pAttrBaseType->getAttrDesc());
}
void CustomAttributeWidget::slotDoubleClicked(QtBrowserItem * item)
{
    if (item == nullptr)
        return;
    //获取Property
    QtProperty* p = item->property();
    if (p == nullptr)
        return;
    //查到到对应的数据对象
    TypeAttributeBase* pBase = this->findTypeAttrBase(p);
    if (pBase == nullptr)
        return;
    const auto& attrDesc = pBase->getAttrDesc();
    WDUnused(attrDesc);
}

void CustomAttributeWidget::clearAttrTypes()
{
    for (auto& itr : _typeAttrBaseMap)
    {
        if (itr.second != nullptr)
        {
            delete itr.second;
        }
    }
    _typeAttrBaseMap.clear();

    _pGroupMgr->clear();

    _pIntMgr->clear();
    _pIntMgrOnlyRead->clear();

    _pStringMgr->clear();
    _pStringMgrOnlyRead->clear();

    _pMyLineEditStringMgr->clear();
    _pMyLineEditStringMgrOnlyRead->clear();

    _pVariantMgr->clear();
    _pVariantMgrOnlyRead->clear();

    _pColorMgr->clear();
    _pColorMgrOnlyRead->clear();

    _pDoubleMgr->clear();
    _pDoubleMgrOnlyRead->clear();

    _pEnumMgr->clear();
    _pEnumMgrOnlyRead->clear();
}

TypeAttributeBase* CustomAttributeWidget::findTypeAttrBase(const std::string_view& name) const
{
    auto fItr = _typeAttrBaseMap.find(name);
    if (fItr == _typeAttrBaseMap.end())
        return nullptr;
    return fItr->second;
}
TypeAttributeBase* CustomAttributeWidget::findTypeAttrBase(QtProperty* pPty) const
{
    if (pPty == nullptr)
        return nullptr;
    for (auto& itr : _typeAttrBaseMap)
    {
        auto pTypeAttrBase = itr.second;
        if (pTypeAttrBase == nullptr)
            continue;
        if(pTypeAttrBase->contains(pPty))
            return pTypeAttrBase;
    }
    return nullptr;
}

void CustomAttributeWidget::blockValueChangedSignals(bool bBlock)
{
    if (bBlock)
    {
        disconnect(_pIntMgr, &QtIntPropertyManager::valueChanged
            , this, &CustomAttributeWidget::slotIntValueChanged);

        disconnect(_pStringMgr, &QtStringPropertyManager::valueChanged
            , this, &CustomAttributeWidget::slotStringValueChanged);

        disconnect(_pMyLineEditStringMgr, &QtStringPropertyManager::valueChanged
            , this, &CustomAttributeWidget::slotStringValueChanged);
        // CE按钮被点击
        disconnect(_pMyLineEditStringMgr, &QtStringPropertyManager::clicked
            , this, &CustomAttributeWidget::slotNodeRefCEClicked);

        disconnect(_pVariantMgr, &QtVariantPropertyManager::valueChanged
            , this, &CustomAttributeWidget::slotVariantValueChanged);

        disconnect(_pColorMgr, &QtColorPropertyManager::valueChanged
            , this, &CustomAttributeWidget::slotColorValueChanged);

        disconnect(_pDoubleMgr, &QtDoublePropertyManager::valueChanged
            , this, &CustomAttributeWidget::slotDoubleValueChanged);

        disconnect(_pEnumMgr, &QtEnumPropertyManager::valueChanged
            , this, &CustomAttributeWidget::slotEnumValueChanged);
    }
    else
    {
        connect(_pIntMgr, &QtIntPropertyManager::valueChanged
            , this, &CustomAttributeWidget::slotIntValueChanged);

        connect(_pStringMgr, &QtStringPropertyManager::valueChanged
            , this, &CustomAttributeWidget::slotStringValueChanged);

        connect(_pMyLineEditStringMgr, &QtStringPropertyManager::valueChanged
            , this, &CustomAttributeWidget::slotStringValueChanged);
        // CE按钮被点击
        connect(_pMyLineEditStringMgr, &QtStringPropertyManager::clicked
            , this, &CustomAttributeWidget::slotNodeRefCEClicked);

        connect(_pVariantMgr, &QtVariantPropertyManager::valueChanged
            , this, &CustomAttributeWidget::slotVariantValueChanged);

        connect(_pColorMgr, &QtColorPropertyManager::valueChanged
            , this, &CustomAttributeWidget::slotColorValueChanged);

        connect(_pDoubleMgr, &QtDoublePropertyManager::valueChanged
            , this, &CustomAttributeWidget::slotDoubleValueChanged);

        connect(_pEnumMgr, &QtEnumPropertyManager::valueChanged
            , this, &CustomAttributeWidget::slotEnumValueChanged);
    }
}

void CustomAttributeWidget::initCreatePropertyMgrs()
{
    // 创建属性数界面对象
    _pWidget = new QtTreePropertyBrowser();
    _pGroupMgr = new QtGroupPropertyManager(_pWidget);

    _pIntMgr = new QtIntPropertyManager(_pWidget);
    _pIntMgrOnlyRead = new QtIntPropertyManager(_pWidget);
    _pIntFactory = new QtSpinBoxFactory(_pWidget);
    _pWidget->setFactoryForManager(_pIntMgr, _pIntFactory);
    connect(_pIntMgr, &QtIntPropertyManager::valueChanged
        , this, &CustomAttributeWidget::slotIntValueChanged);

    _pStringMgr = new QtStringPropertyManager(_pWidget);
    _pStringMgrOnlyRead = new QtStringPropertyManager(_pWidget);
    _pStringFactory = new QtLineEditFactory(_pWidget);
    _pWidget->setFactoryForManager(_pStringMgr, _pStringFactory);
    connect(_pStringMgr, &QtStringPropertyManager::valueChanged
        , this, &CustomAttributeWidget::slotStringValueChanged);

    _pMyLineEditStringMgr = new QtStringPropertyManager(_pWidget);
    _pMyLineEditStringMgrOnlyRead = new QtStringPropertyManager(_pWidget);
    _pMyLineEditStringFactory = new MyLineEditFactory(_pWidget);
    _pWidget->setFactoryForManager(_pMyLineEditStringMgr, _pMyLineEditStringFactory);
    connect(_pMyLineEditStringMgr, &QtStringPropertyManager::valueChanged
        , this, &CustomAttributeWidget::slotStringValueChanged);
    // CE按钮被点击
    connect(_pMyLineEditStringMgr, &QtStringPropertyManager::clicked
        , this, &CustomAttributeWidget::slotNodeRefCEClicked);

    _pVariantMgr = new QtVariantPropertyManager(_pWidget);
    _pVariantMgrOnlyRead = new QtVariantPropertyManager(_pWidget);
    _pVariantFactory = new QtVariantEditorFactory(_pWidget);
    _pWidget->setFactoryForManager(_pVariantMgr, _pVariantFactory);
    connect(_pVariantMgr, &QtVariantPropertyManager::valueChanged
        , this, &CustomAttributeWidget::slotVariantValueChanged);

    _pColorMgr = new QtColorPropertyManager(_pWidget);
    _pColorMgrOnlyRead = new QtColorPropertyManager(_pWidget);
    _pColorFactory = new QtColorEditorFactory(_pWidget);
    _pWidget->setFactoryForManager(_pColorMgr, _pColorFactory);
    connect(_pColorMgr, &QtColorPropertyManager::valueChanged
        , this, &CustomAttributeWidget::slotColorValueChanged);

    _pDoubleMgr = new QtDoublePropertyManager(_pWidget);
    _pDoubleMgrOnlyRead = new QtDoublePropertyManager(_pWidget);
    _pDoubleFactory = new QtDoubleSpinBoxFactory(_pWidget);
    _pWidget->setFactoryForManager(_pDoubleMgr, _pDoubleFactory);
    connect(_pDoubleMgr, &QtDoublePropertyManager::valueChanged
        , this, &CustomAttributeWidget::slotDoubleValueChanged);

    _pEnumMgr = new QtEnumPropertyManager(_pWidget);
    _pEnumMgrOnlyRead = new QtEnumPropertyManager(_pWidget);
    _pEnumFactory = new QtEnumEditorFactory(_pWidget);
    _pWidget->setFactoryForManager(_pEnumMgr, _pEnumFactory);
    connect(_pEnumMgr, &QtEnumPropertyManager::valueChanged
        , this, &CustomAttributeWidget::slotEnumValueChanged);

    // 监听双击item
    connect(_pWidget, &QtTreePropertyBrowser::doubleClicked
        , this, &CustomAttributeWidget::slotDoubleClicked);
}
template <typename T>
void FreePtr(T*& pPtr) 
{
    if (pPtr != nullptr) 
    {
        delete pPtr;
        pPtr = nullptr;
    }
}
void CustomAttributeWidget::uninitDestroyPropertyMgrs()
{
    FreePtr(_pEnumFactory);
    FreePtr(_pEnumMgrOnlyRead);
    FreePtr(_pEnumMgr);

    FreePtr(_pDoubleFactory);
    FreePtr(_pDoubleMgrOnlyRead);
    FreePtr(_pDoubleMgr);
   
    FreePtr(_pColorFactory);
    FreePtr(_pColorMgrOnlyRead);
    FreePtr(_pColorMgr);

    FreePtr(_pVariantFactory);
    FreePtr(_pVariantMgrOnlyRead);
    FreePtr(_pVariantMgr);

    FreePtr(_pMyLineEditStringFactory);
    FreePtr(_pMyLineEditStringMgrOnlyRead);
    FreePtr(_pMyLineEditStringMgr);

    FreePtr(_pStringFactory);
    FreePtr(_pStringMgrOnlyRead);
    FreePtr(_pStringMgr);

    FreePtr(_pIntFactory);
    FreePtr(_pIntMgrOnlyRead);
    FreePtr(_pIntMgr);

    FreePtr(_pGroupMgr);
    
    FreePtr(_pWidget);
}

std::string CustomAttributeWidget::nameTS(const std::string& name, int type, const std::string& tsContextName)
{
    if (!_nameTSFunc)
        return name;

    NameTsType tType = NameTsType::NTT_Attr;
    switch (type)
    {
    case TypeAttributeBase::NTT_Attr:
        type = NameTsType::NTT_Attr;
        break;
    case TypeAttributeBase::NTT_Type:
        type = NameTsType::NTT_Type;
        break;
    case TypeAttributeBase::NTT_EnumItem:
        type = NameTsType::NTT_EnumItem;
        break;
    case TypeAttributeBase::NTT_Others:
        type = NameTsType::NTT_Others;
        break;
    default:
        break;
    }
    return _nameTSFunc(name, tType, tsContextName);
}
