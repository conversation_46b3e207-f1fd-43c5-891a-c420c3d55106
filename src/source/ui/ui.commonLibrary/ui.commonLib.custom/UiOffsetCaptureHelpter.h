#pragma once

#include <QObject>
#include "core/WDCore.h"
#include "core/viewer/capturePositioning/WDCapturePositioning.h"

class QDoubleSpinBox;
class QCheckBox;

/**
 * @brief 通过捕捉工具来确定一个偏移量的界面辅助对象
*/
class UiOffsetCaptureHelpterPrivate;
class UiOffsetCaptureHelpter : public QObject
{
    Q_OBJECT
public:
    /**
     * @brief 捕捉次数
    */
    enum CaptureTimes
    {
        // 一次，表示通过按钮进入捕捉状态后，捕捉到一次结果立即退出捕捉状态
        CT_Once = 0,
        // 重复，表示通过按钮进入捕捉状态后，捕捉到结果时不会退出捕捉状态，直到再次通过按钮手动退出捕捉状态
        CT_Repeat,
    };
    /**
     * @brief 节点过滤通知
     * @param node 节点对象
     * @return 该节点是否被过滤
    */
    using NodeFilter = std::function<bool(WD::WDNode& node, const UiOffsetCaptureHelpter& sender)>;
public:
    UiOffsetCaptureHelpter(WD::WDCore& core);
    UiOffsetCaptureHelpter(const UiOffsetCaptureHelpter& right) = delete;
    UiOffsetCaptureHelpter(UiOffsetCaptureHelpter&& right) = delete;
    UiOffsetCaptureHelpter operator=(const UiOffsetCaptureHelpter& right) = delete;
    UiOffsetCaptureHelpter operator=(UiOffsetCaptureHelpter&& right) = delete;
    ~UiOffsetCaptureHelpter();
signals:
    /**
     * @brief 偏移量改变信号
     * @param currOffset 当前位置
     *      基于当前变换矩阵(transform)描述的坐标系
     *      因此 世界坐标系的 currOffset: gCurrOffset = WD::DMat4::ToMat3(transform) * currOffset
     * @param prevOffset 改变之前的位置
     *      基于当前变换矩阵(transform)描述的坐标系
     *      因此 世界坐标系的 prevOffset: gPrevOffset = WD::DMat4::ToMat3(transform) * gPrevOffset
     * @param transform 当前的变换矩阵
    */
    void sigOffsetChanged(const WD::DVec3& currOffset, const WD::DVec3& prevOffset, const WD::DMat4& transform);
public:
    /**
     * @brief 设置捕捉参数
    */
    inline void setCaptureParam(const WD::WDCapturePositioningParam& param) 
    {
        if (_param == param)
            return;
        _param = param;
    }
    /**
     * @brief 获取捕捉参数
    */
    inline const WD::WDCapturePositioningParam& captureParam() const
    {
        return _param;
    }

    /**
     * @brief 设置捕捉次数 (默认值为: CaptureTimes::CT_OneTime)
     * @param times @see ref CaptureTimes
    */
    inline void setCaptureTimes(CaptureTimes times)
    {
        _captureTimes = times;
    }
    /**
     * @brief 获取捕捉次数
    */
    inline CaptureTimes captureTimes() const
    {
        return _captureTimes;
    }

    /**
     * @brief 设置变换矩阵
     *  用于将偏移量描述到当前变换矩阵描述的坐标系下，再显示到界面
    */
    void setTransform(const WD::DMat4& transform);
    /**
     * @brief 获取变换矩阵
    */
    inline const WD::DMat4& transform() const
    {
        return _transform;
    }

    /**
     * @brief 设置偏移量
     * @param offset 偏移量(世界坐标/基于transform描述的坐标系)
     * @param bBasedTransform 如果值为true 表示设置的偏移量基于transform描述的坐标系，否则将视为基于世界坐标系
    */
    void setOffset(const WD::DVec3& offset, bool bBasedTransform = false);
    /**
     * @brief 通过指定两个点来设置偏移量, 其中:偏移量 = ptB - ptA
     * @param ptA A点
     * @param ptB B点
     * @param bABasedTransform A点是否基于transform描述的坐标系，如果不是基于transform描述的坐标系，将视为世界坐标系运算
     * @param bBBasedTransform B点是否基于transform描述的坐标系，如果不是基于transform描述的坐标系，将视为世界坐标系运算
    */
    void setOffsetByPositions(const WD::DVec3& ptA
        , const WD::DVec3& ptB
        , bool bABasedTransform = false
        , bool bBBasedTransform = false);
    /**
     * @brief 获取偏移量
     * @param bBasedTransform 如果值为true 表示获取的位置将基于transform描述的坐标系，否则将基于世界坐标系
     * @return 位置(世界坐标)
    */
    WD::DVec3 offset(bool bBasedTransform = false) const;

    /**
     * @brief 退出
     *      如果当前是捕捉状态，则退出捕捉状态
     * @param bResetUi 是否要重置界面(包括三个DoubleSpinBox和三个CheckBox)
    */
    void exit(bool bResetUi);
    /**
     * @brief 用于通知Undo command执行
    */
    using NoticeUndoCommandExecution = std::function<void(WD::WDNode::SharedPtr pNode
        , const UiOffsetCaptureHelpter& sender)>;
    /**
     * @brief 应用界面上的位置到指定的节点
     * @param node 节点
     * @param bResetOffset 是否重置偏移量,如果为true，表示此次偏移量应用给节点之后会重置为WD::DVec3::Zero()
     * @param pUndoStack undo-redo栈，可以给空，当不为空时，将会向栈中加入一个用于撤销此次节点位置改变操作的命令
     * @param onUndoCommandExecution 如果指定了undo-redo栈，还可以指定一个通知对象
     *      用于向栈中添加的undo command 执行了之后通知给调用端，可以在此回调用刷新界面显示
     *      这里使用智能指针，原因是可能调用端对象已被释放而undo command还未被释放
     *      因此undo command 内部会保存该通知对象的弱引用而调用端对象保存其强引用，当调用端对象释放时，弱引用将失效，也将不会触发回调
    */
    void applyOffsetToNode(WD::WDNode::SharedPtr pNode
        , WD::WDUndoStack* pUndoStack = nullptr
        , NoticeUndoCommandExecution notice = NoticeUndoCommandExecution()) const;

    /**
     * @brief 设置用于显示 X,Y,Z坐标的 QDoubleSpinBox
     * @param pDSpinBoxX 用于显示X坐标, 可以给nullptr,表示不设置/获取该分量
     * @param pDSpinBoxY 用于显示Y坐标, 可以给nullptr,表示不设置/获取该分量
     * @param pDSpinBoxZ 用于显示Z坐标, 可以给nullptr,表示不设置/获取该分量
    */
    void setDoubleSpinBoxXYZ(QDoubleSpinBox* pDSpinBoxX
        , QDoubleSpinBox* pDSpinBoxY
        , QDoubleSpinBox* pDSpinBoxZ);
    /**
     * @brief 设置用于 开启/关闭 捕捉功能的 QCheckBox
     * @param pCheckBoxCapture 用于 开启/关闭 捕捉功能的 QCheckBox
    */
    void setCheckBoxCapture(QCheckBox* pCheckBoxCapture);
    /**
     * @brief 设置用于锁定 X,Y,Z坐标的 QDoubleSpinBox的 QCheckBox
     * @param pCheckBoxX 用于锁定pDSpinBoxX,使其只读
     * @param pCheckBoxY 用于锁定pDSpinBoxY,使其只读
     * @param pCheckBoxZ 用于锁定pDSpinBoxZ,使其只读
    */
    void setCheckBoxXYZ(QCheckBox* pCheckBoxX
        , QCheckBox* pCheckBoxY
        , QCheckBox* pCheckBoxZ);

    /**
     * @brief 获取当前捕捉的结果点列表
     *      该列表最大长度不超过2(因为偏移量只需要最多两个点就可以确定)
     *      当长度为0时，表明没有捕捉
     *      当长度为1时，表明只捕捉了一个点，等待继续捕捉第二个点
     *      当长度为2时，表明两个点都已经捕捉到，偏移量已经确定
     * @return 捕捉的结果点列表(世界坐标)
    */
    const std::vector<WD::DVec3>& capturePoints() const;

    /**
     * @brief 设置节点过滤回调
    */
    inline void setNodeFilter(NodeFilter filter)
    {
        _nodeFilter = filter;
    }
private slots:
    // 界面输入值改变
    void slotDSpinBoxValueChanged();
    // 锁定状态改变
    void slotCheckBoxXYZStateChanged(int state);
    // 捕捉状态改变
    void slotCheckBoxCaptureClicked(bool );
private:
    /**
     * @brief 更新偏移信息到辅助显示界面
     * @param offset 期望更新的位移
     * @return 实际更新的位置
    */
    void updateOffsetDisplay();
private:
    WD::WDCore&                     _core;
    // 捕捉参数
    WD::WDCapturePositioningParam   _param;
    // 每次开启捕捉之后，捕捉的次数类型
    CaptureTimes                    _captureTimes;
    // 保存变换矩阵(用于指定坐标系)
    WD::DMat4                       _transform;
    // 保存当前基于变换矩阵的偏移量信息
    WD::DVec3                       _offset;

    // 用于辅助显示偏移信息的QDoubleSpinBox
    QDoubleSpinBox* _pDSpinBoxX;
    QDoubleSpinBox* _pDSpinBoxY;
    QDoubleSpinBox* _pDSpinBoxZ;
    // 用于辅助开启/关闭捕捉的QCheckBox
    QCheckBox*      _pCheckBoxCapture;
    // 用于锁定辅助显示偏移信息界面的QCheckBox
    QCheckBox*      _pCheckBoxX;
    QCheckBox*      _pCheckBoxY;
    QCheckBox*      _pCheckBoxZ;

    // 节点过滤回调
    NodeFilter      _nodeFilter;

    UiOffsetCaptureHelpterPrivate* _p;
    friend class UiOffsetCaptureHelpterPrivate;
};