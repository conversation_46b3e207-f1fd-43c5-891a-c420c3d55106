#pragma once
#include "ComplicateType.h"

class ComplicateVectorType: public PropertyBaseType
{
private:
    QtAbstractPropertyManager*          _pManager;
    QtGroupPropertyManager*             _pGroupMgr;
    QtStringPropertyManager*            _pStringMgr;
    QtEnumPropertyManager*              _pEnumMgr;
    QtProperty*                         _pRootProperty;
    std::vector<ComplicateBaseType*>    _pTypeVector;
    //存储数组长度
    size_t                              _vectorSize;

public:
    ComplicateVectorType(ObjectPropertyWidget& ownWidget
    , QtGroupPropertyManager* pGroupMgr
    , QtStringPropertyManager* pStringMgr
    , QtAbstractPropertyManager* pManager
    , WD::WDProperty::SharedPtr pPtr
    , QtEnumPropertyManager* pEnumMgr = nullptr);
    ~ComplicateVectorType();
public:
    /**
    * @brief WDProperty的值更新显示界面
    * @return true 更新成功 false 更新失败
    */
    virtual bool updateValueFromWDPty() override;
    /**
    * @brief 将保存的值应用到保存的WDProperty对象上
    * @return true 应用成功 false 应用失败
    */
    virtual bool applyValue() override;
    /**
    * @brief 是否包含此属性栏对象
    * @param pPty 属性栏对象
    * @return true 包含 false 不包含
    */
    virtual bool contains(QtProperty* pPty) const override;
    /**
    * @brief 返回根属性栏对象
    */
    virtual QtProperty* getRootProperty() override;
protected:
    /**
    * @brief 更新所有QtProperty显示
    */
    virtual bool updateShowP(QtProperty* pPty) override;
};
