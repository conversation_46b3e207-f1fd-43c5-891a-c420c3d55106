#include "WDProperty.h"

WD_NAMESPACE_BEGIN

WDProperty::WDProperty(WDPropertyDataType dType, const std::string& name, bool bEditable)
    :WDObject(name)
{
    _dataType       = dType;
    _bEditable      = bEditable;
}
WDProperty::~WDProperty()
{
}

void WDProperty::copy(const WDObject* pSrcObject)
{
    const WDProperty* pSrc = dynamic_cast<const WDProperty*>(pSrcObject);
    if (pSrc == nullptr)
        return;
    //类型不同，无法拷贝
    if (this->_dataType != pSrc->_dataType)
        return;

    WDObject::copy(pSrc);

    this->setEditable(pSrc->editable());
}
WDObject::SharedPtr WDProperty::clone() const
{
    return nullptr;
}



WD_NAMESPACE_END