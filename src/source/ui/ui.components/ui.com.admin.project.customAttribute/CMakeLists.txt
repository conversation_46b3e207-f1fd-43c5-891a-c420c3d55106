set(TARGET_NAME ui_com_admin_project_customAttribute)

set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTOUIC ON)
set(CMAKE_AUTORCC ON) 
find_package(Qt5 COMPONENTS Core Widgets Network REQUIRED)

set(HEADER_FILES
	"UiComAdminProCustomAttr.h"
	"ProCustomAttrDialog.h"
	"AttributeListWidget.h"
	"TypeListWidget.h"
	"AttributeInfoWidget.h"
	"TypeInfo.h"
)

set(SOURCE_FILES
	"UiComAdminProCustomAttr.cpp"
	"ProCustomAttrDialog.cpp"
	"TypeListWidget.cpp"
	"AttributeListWidget.cpp"
	"AttributeInfoWidget.cpp"
	"main.cpp"
)

set(FORM_FILES
	"ProCustomAttrDialog.ui"
	"TypeListWidget.ui"
	"AttributeListWidget.ui"
	"AttributeInfoWidget.ui"
)

add_library(${TARGET_NAME} SHARED
		${HEADER_FILES}
		${SOURCE_FILES}
		${FORM_FILES}
)
add_compile_definitions(${TARGET_NAME} PRIVATE UI_COM_ADMIN_DATA_LIB)

target_link_libraries(${TARGET_NAME} PUBLIC wizDesignerCore ui.commonLib.custom util.rapidxml util.rapidjson)
target_link_libraries(${TARGET_NAME} PUBLIC Qt5::Core Qt5::Widgets QScintilla qtpropertybrowser Qt5::Network)
target_include_directories(${TARGET_NAME} PRIVATE ${CMAKE_CURRENT_SOURCE_DIR})

add_custom_command(
	TARGET ${TARGET_NAME}
	POST_BUILD
    COMMAND             ${CMAKE_COMMAND} -E make_directory ./translations
    COMMAND             ${CMAKE_COMMAND} -E copy_directory ./translations ${CMAKE_RUNTIME_OUTPUT_DIRECTORY}/ui/components/${TARGET_NAME}/translations
	WORKING_DIRECTORY	${CMAKE_CURRENT_SOURCE_DIR}
    COMMENT             "copy directory translations..."
)