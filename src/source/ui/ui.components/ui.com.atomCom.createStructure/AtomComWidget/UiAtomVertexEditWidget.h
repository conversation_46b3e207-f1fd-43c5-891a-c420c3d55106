#pragma once

#include <QWidget>
#include "ui_UiAtomVertexEditWidget.h"
#include "core/WDCore.h"
#include "core/node/WDNode.h"

struct TableWidgetItemData
{
    WD::WDNode::WeakPtr _pSpve;             // ��Ӧ��SPVE�ڵ�������
public:
    TableWidgetItemData(){}
    TableWidgetItemData(WD::WDNode::SharedPtr pSpve)
        : _pSpve(pSpve){}
};
Q_DECLARE_METATYPE(TableWidgetItemData)

class UiAtomVertexEditWidget : public QWidget
{
    Q_OBJECT
public:
    UiAtomVertexEditWidget(QWidget *parent = Q_NULLPTR);
    ~UiAtomVertexEditWidget();
private:
    // ����Excelʱ������ʶ����
    enum DataFlag
    {
        Old = 0,
        New = 1
    };
public:
    //ʹ��SPRO�ڵ��ʼ�����㴴�����
    bool initVertexEditTable(WD::WDNode::SharedPtr pSpro);
public slots:
    // item�޸Ĳۺ���
    void slotOnItemTextChanged(QTableWidgetItem *);
    // Add��ť����ۺ���
    void slotOnAddButtonClicked();
    // Copy��ť����ۺ���
    void slotOnCopyButtonClicked();
    // Delete��ť����ۺ���
    void slotOnDeleteButtonClicked();
    // Import��ť����ۺ���
    void slotOnImportButtonClicked();
    // Export��ť����ۺ���
    void slotOnExportButtonClicked();
private:
    WD::StringVector getRowItemTexts(int rowIndex);

    /**
    * @brief ʹ��SPVE�ڵ������½�һ��Items
    */
    bool createRowBySpveNode(WD::WDNode::SharedPtr pSpve);
    /**
    * @brief �ַ�������ת��ΪSPVE�ڵ������
    */
    bool stringVectorToSPVEAttr(WD::WDNode& spve, const WD::StringVector& stringVec);
private:
    Ui::UiAtomVertexEditWidget           ui;
    // ����Ĳ�����SPRO�ڵ�
    WD::WDNode::WeakPtr                  _pSpro;
};
