#pragma once

#include "ui_CollisionResultTableWidget.h"
#include <QWidget>
#include "UiHeaderView.h"
#include "CollisionCheckCommon.h"
#include <QSortFilterProxyModel>



class ResultTableModel : public QAbstractItemModel
{
    Q_OBJECT
private:
    // 表头数据
    QStringList _headLabels;
    // 碰撞类型的字符串
    std::map<CsClashType, QString> _clashTypeStrs;
    // 碰撞体空间占有属性的字符串
    std::map<CsObst, QString> _obstStrs;
    // 结果数据
    CsResults _rets;
    // 表头文本后缀
    std::map<int, QString> _headSuffix;
public:
    /**
     * @brief 设置水平表头
    */
    void setHeadLabels(const QStringList& labels);
    /**
     * @brief 添加表头后缀
     * @param col 列
     * @param suffix 后缀 
    */
    void addHeadLabelSuffix(int col, QString suffix);
    /**
     * @brief 移除列表头后缀
     * @param col 列
    */
    void removeHeadLabelSuffix(int col);
    /**
     * @brief 清除表头后缀
    */
    void clearHeadLabelSuffix();
    /**
     * @brief 设置碰撞类型的字符串，用于显示
    */
    void setClashTypeStrs(const std::map<CsClashType, QString>& typeStrs);
    /**
     * @brief 设置空间占有属性类型的字符串，用于显示
    */
    void setObstStrs(const std::map<CsObst, QString>& obstStrs);
    /**
     * @brief 刷新
    */
    void refresh(const CsResults& rets = CsResults());
    /**
     * @brief 根据指定的行获取结果数据
     * @param row 行
     * @return 结果数据, 如果指定的行无效，则返回nullptr
    */
    const CsResult* result(int number) const;
public:
    virtual QVariant headerData(int section, Qt::Orientation orientation,
        int role = Qt::DisplayRole) const override;
    virtual QModelIndex index(int row, int column,
        const QModelIndex& parent = QModelIndex()) const override;
    virtual QModelIndex parent(const QModelIndex& child) const override;
    virtual int rowCount(const QModelIndex& parent = QModelIndex()) const override;
    virtual int columnCount(const QModelIndex& parent = QModelIndex()) const override;
    virtual QVariant data(const QModelIndex& index, int role = Qt::DisplayRole) const override;
};

class ResultTableFilterProxyModel :public QSortFilterProxyModel 
{
    Q_OBJECT
public:
    /**
     * @brief 过滤数据
    */
    using FilterData = std::variant<std::monostate
        , QRegExp
        , WD::WDNode::WeakPtr>;
private:
    std::map<int, FilterData> _filterDatas;
public:
    void addFilterData(int col, const FilterData& fData);
    void removeFilterData(int col);
    void clearFilterData();
protected:
    virtual bool filterAcceptsRow(int source_row, const QModelIndex& source_parent) const override;
};

class ResultSubjectTableModel : public QAbstractItemModel
{
    Q_OBJECT
private:
    // 表头数据
    QStringList _headLabels;
    // 主体以及与该主体碰撞的客体个数
    using RowData = std::pair<WD::WDNode::WeakPtr, int>;
    using RowDatas = std::vector<RowData>;
    RowDatas _rowDatas;
public:
    /**
     * @brief 设置水平表头
    */
    void setHeadLabels(const QStringList& labels);
    /**
     * @brief 刷新
    */
    void refresh(const CsResults& rets = CsResults());
    /**
     * @brief 获取指定行的节点
    */
    WD::WDNode::WeakPtr node(int row) const;
public:
    virtual QVariant headerData(int section, Qt::Orientation orientation,
        int role = Qt::DisplayRole) const override;
    virtual QModelIndex index(int row, int column,
        const QModelIndex& parent = QModelIndex()) const override;
    virtual QModelIndex parent(const QModelIndex& child) const override;
    virtual int rowCount(const QModelIndex& parent = QModelIndex()) const override;
    virtual int columnCount(const QModelIndex& parent = QModelIndex()) const override;
    virtual QVariant data(const QModelIndex& index, int role = Qt::DisplayRole) const override;
};

class MyModel;
class QAbstractItemModel;
class QSortFilterProxyModel;

class CollisionResultTableWidget : public QWidget
{
    Q_OBJECT
public:
    /**
     * @brief 定位类型
    */
    enum LocationType
    {
        // 无
        LT_None = 0,
        // 主体
        LT_Subject,
        // 客体
        LT_Object,
    };
public:
    CollisionResultTableWidget(WD::WDCore& app, QWidget *parent = nullptr);
    ~CollisionResultTableWidget();
signals:
    
    /**
     * @brief 当前选中记录的Number(CsResult::number)
    */
    void sigCurrentNumberChanged(int currNumber, int prevNumber);
    /**
    *   @brief 生成表单按键按下信号
    */
    void sigGenerateFormClicked();
public:
    /**
     * @brief 清除结果数据
    */
    void clearResults();
    /**
     * @brief 设置结果数据
    */
    void setResults(const CsResults& rets);
    /**
     * @brief 获取当前选中记录的number
    */
    int currentNumber() const;
    /**
     * @brief 根据指定的行获取结果数据
     * @param number 记录的number
     * @return 结果数据, 如果指定的行无效，则返回nullptr
    */
    const CsResult* result(int number) const;
    /**
     * @brief 获取当前选中行的结果数据
     * @return 结果数据, 如果当前选中的行无效，则返回nullptr
    */
    const CsResult* currentResult() const
    {
        return this->result(this->currentNumber());
    }
    /**
     * @brief 获取定位类型
    */
    LocationType locationType() const;
    /**
     * @brief 设置碰撞主体表格显隐
    */
    void setSubjectTableVisible(bool visible);
private:
    // 界面翻译
    void retranslateUi();
private:
    Ui::CollisionResultTableWidget ui;
    WD::WDCore& _app;
    // 表头
    UiHeaderView* _pHeader;
    // 数据模型
    ResultTableModel* _pModel;
    // 数据过滤
    ResultTableFilterProxyModel* _proxyModel;
    // 只包含碰撞主体的表格
    ResultSubjectTableModel* _pSubjectModel;
    // 翻译后的 "全部" 文本，用于过滤
    QString _allTsStr;
};
