#pragma once

#include "ui_CollisionVolumeWidget.h"
#include <QWidget>
#include "core/WDCore.h"
#include "../../ui.commonLibrary/ui.commonLib.custom/UiPositionCaptureHelpter.h"

/**
 * @brief 碰撞空间配置界面
*/
class CollisionVolumeWidget : public QWidget
{
    Q_OBJECT
public:
    CollisionVolumeWidget(WD::WDCore& app, QWidget *parent = nullptr);
    ~CollisionVolumeWidget();
public:
    /**
     * @brief 获取碰撞空间包围盒
     * @param isInfinite 可选项，用于指示返回的空间包围盒是否为无限大
    */
    WD::DAabb3 volume(bool* isInfinite = nullptr) const;
    /**
     * @brief 是否显示碰撞空间包围盒
    */
    inline bool displayVolume() const
    {
        return ui.checkBoxDisplay->isChecked();
    }
    /**
     * @brief 显示碰撞空间包围盒的颜色
    */
    inline WD::Color displayVolumeColor() const
    {
        QColor color = this->getLabelColor(*ui.labelColor);
        return WD::Color(
            static_cast<WD::byte>(color.red())
            , static_cast<WD::byte>(color.green())
            , static_cast<WD::byte>(color.blue())
            , static_cast<WD::byte>(color.alpha())
        );
    }
protected:
    void showEvent(QShowEvent*) override;
    void hideEvent(QHideEvent*) override;
private:
    // 给Label设置颜色
    void setLabelColor(QLabel& label, const QColor& color);
    // 获取给Label设置的颜色
    QColor getLabelColor(const QLabel& label) const;

    inline void setUData(const QObject& object, const QString& uData)
    {
        _uDataMap[&object] = uData;
    }
    inline QString getUData(const QObject& object) const
    {
        auto fItr = _uDataMap.find(&object);
        if (fItr == _uDataMap.end())
            return "";
        return fItr->second;
    }
private:
    // 界面翻译
    void retranslateUi();
private:
    Ui::CollisionVolumeWidget ui;
    WD::WDCore& _app;
    // 管理控件的用户数据
    std::map<const QObject*, QString> _uDataMap;
    // 位置捕捉帮助对象
    UiPositionCaptureHelpter _minPosHelpter;
    UiPositionCaptureHelpter _maxPosHelpter;
};
