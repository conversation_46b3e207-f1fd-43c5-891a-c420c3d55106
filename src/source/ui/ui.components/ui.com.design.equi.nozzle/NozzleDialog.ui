<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>NozzleDialog</class>
 <widget class="QDialog" name="NozzleDialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>294</width>
    <height>467</height>
   </rect>
  </property>
  <property name="focusPolicy">
   <enum>Qt::StrongFocus</enum>
  </property>
  <property name="windowTitle">
   <string>Create Nozzle</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_2"/>
   </item>
   <item>
    <widget class="QGroupBox" name="groupBoxGradeSpecification">
     <property name="title">
      <string>Grade Specification</string>
     </property>
     <layout class="QGridLayout" name="gridLayout">
      <item row="1" column="0">
       <widget class="QLabel" name="labelType">
        <property name="text">
         <string>Type</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignCenter</set>
        </property>
       </widget>
      </item>
      <item row="0" column="0">
       <widget class="QLabel" name="labelStandard">
        <property name="layoutDirection">
         <enum>Qt::LeftToRight</enum>
        </property>
        <property name="text">
         <string>Standard</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignCenter</set>
        </property>
       </widget>
      </item>
      <item row="3" column="0">
       <widget class="QLabel" name="labelGrade">
        <property name="text">
         <string>Grade</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignCenter</set>
        </property>
       </widget>
      </item>
      <item row="2" column="0">
       <widget class="QLabel" name="labelPipeDiameter">
        <property name="text">
         <string>Pipe Diameter</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignCenter</set>
        </property>
       </widget>
      </item>
      <item row="0" column="1">
       <widget class="QComboBox" name="standardComboBox">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
       </widget>
      </item>
      <item row="1" column="1">
       <widget class="QComboBox" name="typeComboBox">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
       </widget>
      </item>
      <item row="2" column="1">
       <widget class="QComboBox" name="pipeDiametercomboBox">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
       </widget>
      </item>
      <item row="3" column="1">
       <widget class="QLineEdit" name="levelLineEdit">
        <property name="focusPolicy">
         <enum>Qt::StrongFocus</enum>
        </property>
        <property name="readOnly">
         <bool>true</bool>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QGroupBox" name="groupBox">
     <property name="title">
      <string/>
     </property>
     <layout class="QGridLayout" name="gridLayout_2" columnstretch="0,1" columnminimumwidth="0,0">
      <item row="1" column="1">
       <widget class="QDoubleSpinBox" name="doubleSpinBoxHeight">
        <property name="focusPolicy">
         <enum>Qt::WheelFocus</enum>
        </property>
        <property name="maximum">
         <double>999999999999.000000000000000</double>
        </property>
       </widget>
      </item>
      <item row="1" column="0">
       <widget class="QLabel" name="labelHeight">
        <property name="text">
         <string>Height</string>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QGroupBox" name="groupBoxCoordinate">
     <property name="title">
      <string>CapturePosition</string>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout_2">
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout_4">
        <item>
         <widget class="QLabel" name="labelWrt">
          <property name="text">
           <string>WRT</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLineEdit" name="lineEditWrt">
          <property name="readOnly">
           <bool>true</bool>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QCheckBox" name="checkBox">
          <property name="focusPolicy">
           <enum>Qt::StrongFocus</enum>
          </property>
          <property name="text">
           <string>Pickup</string>
          </property>
         </widget>
        </item>
       </layout>
      </item>
      <item>
       <layout class="QFormLayout" name="formLayout">
        <item row="0" column="0">
         <widget class="QLabel" name="label_8">
          <property name="text">
           <string>X</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignCenter</set>
          </property>
         </widget>
        </item>
        <item row="0" column="1">
         <widget class="QDoubleSpinBox" name="doubleSpinBoxPosX">
          <property name="focusPolicy">
           <enum>Qt::WheelFocus</enum>
          </property>
          <property name="decimals">
           <number>4</number>
          </property>
          <property name="minimum">
           <double>-999999999999.000000000000000</double>
          </property>
          <property name="maximum">
           <double>999999999999.000000000000000</double>
          </property>
         </widget>
        </item>
        <item row="1" column="0">
         <widget class="QLabel" name="label_9">
          <property name="text">
           <string>Y</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignCenter</set>
          </property>
         </widget>
        </item>
        <item row="1" column="1">
         <widget class="QDoubleSpinBox" name="doubleSpinBoxPosY">
          <property name="decimals">
           <number>4</number>
          </property>
          <property name="minimum">
           <double>-999999999999.000000000000000</double>
          </property>
          <property name="maximum">
           <double>999999999999.000000000000000</double>
          </property>
         </widget>
        </item>
        <item row="2" column="0">
         <widget class="QLabel" name="label_10">
          <property name="text">
           <string>Z</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignCenter</set>
          </property>
         </widget>
        </item>
        <item row="2" column="1">
         <widget class="QDoubleSpinBox" name="doubleSpinBoxPosZ">
          <property name="decimals">
           <number>4</number>
          </property>
          <property name="minimum">
           <double>-999999999999.000000000000000</double>
          </property>
          <property name="maximum">
           <double>999999999999.000000000000000</double>
          </property>
         </widget>
        </item>
       </layout>
      </item>
      <item>
       <widget class="QLineEdit" name="lineEditDirectionP1">
        <property name="focusPolicy">
         <enum>Qt::WheelFocus</enum>
        </property>
        <property name="text">
         <string>Z</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QLabel" name="labelP1Direction">
        <property name="text">
         <string>P1 Direction</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignCenter</set>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout">
     <item>
      <spacer name="horizontalSpacer">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QLabel" name="label">
       <property name="minimumSize">
        <size>
         <width>100</width>
         <height>0</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>16777215</width>
         <height>16777215</height>
        </size>
       </property>
       <property name="focusPolicy">
        <enum>Qt::NoFocus</enum>
       </property>
       <property name="text">
        <string/>
       </property>
      </widget>
     </item>
     <item>
      <spacer name="horizontalSpacer_2">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QPushButton" name="okButton">
       <property name="focusPolicy">
        <enum>Qt::StrongFocus</enum>
       </property>
       <property name="text">
        <string>Ok</string>
       </property>
       <property name="autoDefault">
        <bool>false</bool>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="cancelButton">
       <property name="focusPolicy">
        <enum>Qt::StrongFocus</enum>
       </property>
       <property name="text">
        <string>Cancel</string>
       </property>
       <property name="autoDefault">
        <bool>false</bool>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <spacer name="verticalSpacer">
     <property name="orientation">
      <enum>Qt::Vertical</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>20</width>
       <height>40</height>
      </size>
     </property>
    </spacer>
   </item>
  </layout>
 </widget>
 <layoutdefault spacing="6" margin="11"/>
 <tabstops>
  <tabstop>standardComboBox</tabstop>
  <tabstop>typeComboBox</tabstop>
  <tabstop>pipeDiametercomboBox</tabstop>
  <tabstop>levelLineEdit</tabstop>
  <tabstop>doubleSpinBoxHeight</tabstop>
  <tabstop>checkBox</tabstop>
  <tabstop>doubleSpinBoxPosX</tabstop>
  <tabstop>doubleSpinBoxPosY</tabstop>
  <tabstop>doubleSpinBoxPosZ</tabstop>
  <tabstop>lineEditDirectionP1</tabstop>
  <tabstop>label</tabstop>
  <tabstop>okButton</tabstop>
  <tabstop>cancelButton</tabstop>
 </tabstops>
 <resources/>
 <connections/>
</ui>
