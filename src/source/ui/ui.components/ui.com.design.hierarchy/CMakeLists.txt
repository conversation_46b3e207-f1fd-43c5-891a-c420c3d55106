set(TARGET_NAME ui_com_design_hierarchy)

set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTOUIC ON)
set(CMAKE_AUTORCC ON) 
find_package(Qt5 COMPONENTS Core Widgets Xml REQUIRED)

set(HEADER_FILES
	"UiComHierarchy.h"
	"HierarchyIncludeDialog.h"
	"includeUndoRedo.h"
)

set(SOURCE_FILES
    "main.cpp"
	"UiComHierarchy.cpp"
	"HierarchyIncludeDialog.cpp"
)

set(FORM_FILES
	"HierarchyIncludeDialog.ui"
)

add_library(${TARGET_NAME} SHARED
		${HEADER_FILES}
		${SOURCE_FILES}
		${FORM_FILES}
)

target_link_libraries(${TARGET_NAME} PUBLIC wizDesignerCore ui.commonLib.property ui.commonLib.custom ui.commonLib.excel ui.commonLib.WeakObject)
target_link_libraries(${TARGET_NAME} PUBLIC Qt5::Core Qt5::Widgets Qt5::Xml QtnRibbon qtpropertybrowser)
target_include_directories(${TARGET_NAME} PRIVATE ${CMAKE_CURRENT_SOURCE_DIR})

add_custom_command(
	TARGET ${TARGET_NAME}
	POST_BUILD
    COMMAND             ${CMAKE_COMMAND} -E make_directory ./translations
    COMMAND             ${CMAKE_COMMAND} -E copy_directory ./translations ${CMAKE_RUNTIME_OUTPUT_DIRECTORY}/ui/components/${TARGET_NAME}/translations
	WORKING_DIRECTORY	${CMAKE_CURRENT_SOURCE_DIR}
    COMMENT             "copy directory translations..."
)