#pragma once

#include <QObject>
#include "../../wizDesignerApp/UiInterface/UiInterface.h"

#include "BranCreateDialog.h"

class UiComCreateBran
    : public QObject
	, public IUiComponent
{
    Q_OBJECT
public:
    UiComCreateBran(IMainWindow& mainWindow, const UiComponentAttributes& attrs, QObject *parent = nullptr);
    ~UiComCreateBran();
    /**
    *   @brief 通知事件
    *   @param pNotice 事件对象
    */
    virtual void onNotice(UiNotice* pNotice) override;

private:
    WD::WDCore&         _core;
    // HVAC对话框
    BranCreateDialog*   _pBranCreateDialog;
};

