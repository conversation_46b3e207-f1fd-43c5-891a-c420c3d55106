set(TARGET_NAME ui_com_design_members)

set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTOUIC ON)
set(CMAKE_AUTORCC ON) 
find_package(Qt5 COMPONENTS Core Widgets REQUIRED)

set(HEADER_FILES
    "UiComMembers.h"
	"MembersDialog.h"
	"ReferenceDialog.h"
)

set(SOURCE_FILES
     "UiComMembers.cpp"
	"MembersDialog.cpp"
	"ReferenceDialog.cpp"
	"main.cpp"
)

set(FORM_FILES
	"MembersDialog.ui" 
	"ReferenceDialog.ui"
)

set(RCC_FILES "membersDialog.qrc")

add_library(${TARGET_NAME} SHARED
		${HEADER_FILES}
		${SOURCE_FILES}
		${FORM_FILES}
		${RCC_FILES}
)

target_link_libraries(${TARGET_NAME} PUBLIC wizDesignerCore ui.commonLib.WeakObject)
target_link_libraries(${TARGET_NAME} PUBLIC Qt5::Core Qt5::Widgets)
target_include_directories(${TARGET_NAME} PRIVATE ${CMAKE_CURRENT_SOURCE_DIR})

add_custom_command(
	TARGET ${TARGET_NAME}
	POST_BUILD
    COMMAND             ${CMAKE_COMMAND} -E make_directory ./translations
    COMMAND             ${CMAKE_COMMAND} -E copy_directory ./translations ${CMAKE_RUNTIME_OUTPUT_DIRECTORY}/ui/components/${TARGET_NAME}/translations
	WORKING_DIRECTORY	${CMAKE_CURRENT_SOURCE_DIR}
    COMMENT             "copy directory translations..."
)