#pragma once

#include <QDialog>
#include "ui_SPCOSelectionDialog.h"
#include "PipeSlopeModelingCommon.h"
#include "WDCore.h"

class SPCOSelectionDialog : public QDialog
{
    Q_OBJECT
private:
    Ui::SPCOSelectionDialog     ui;
    WD::WDCore&                 _core;
    PipeSlopeModelingCommon&    _common;

    // 管道
    static constexpr const char* Pipe_SPEC_Stext = "PIPING";
    // 保温
    static constexpr const char* Insu_SPEC_Stext = "INSU";
    static constexpr const char* Insu_SPEC_Stext1 = "INSUL";
    // 伴热
    static constexpr const char* Trac_SPEC_Stext = "TRACE";

public:
    SPCOSelectionDialog(WD::WDCore& core, PipeSlopeModelingCommon& common, QWidget *parent = Q_NULLPTR);
    ~SPCOSelectionDialog();
public:
    /**
     * @brief 更新给common默认的等级
    */
    void updateDefaultSpecToCommon();
protected:
    virtual void showEvent(QShowEvent* evt) override;
    virtual void hideEvent(QHideEvent* evt) override;
private:
    /**
    * @brief 根据用途数组初始化专业等级列表
    * @param purposes 用途数组
    * @return true 初始化成功 false 初始化失败
    */
    bool updatePSpecList();
    bool updateISpecList();
    bool updateTSpecList();
private slots:
    void slotPushButtonOkClicked();
    void slotPushButtonCancelClicked();
private:
    // 界面翻译
    void retranslateUi();
};
