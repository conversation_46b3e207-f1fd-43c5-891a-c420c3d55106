#pragma once

#include <QDialog>
#include "ui_SupportParamEdit.h"
#include "core/WDCore.h"
#include "WDSupport.h"
#include "../../ui.commonLibrary/ui.commonLib.property/ObjectPropertyWidget.h"
#include "WDSupportMgr.h"
#include <QStyledItemDelegate>

class ReadOnlyDelegate : public QStyledItemDelegate {  
    Q_OBJECT  

public:  
    ReadOnlyDelegate(QObject *parent = nullptr);

    QWidget *createEditor(QWidget *parent, const QStyleOptionViewItem &option, const QModelIndex &index) const;
};

class SupportParamEdit : public QDialog
{
    Q_OBJECT

public:
    SupportParamEdit(WD::WDCore& core, WD::WDSupportMgr& supportMgr, QWidget *parent = Q_NULLPTR);
    ~SupportParamEdit();

private:
    /**
    * @brief 支吊架参数临时数据
    *   -first 支吊架参数引用节点
    *   -second 节点参数序号列表，0为位置，1为朝向
    */
    using TempData = std::map<WD::WDNode::SharedPtr, std::set<int>>;

public:
    /**
    * @brief 设置支吊架模板
    */
    inline void setSupport(WD::WDSupport::SharedPtr pSupport)
    {
        _pSupport = pSupport;
    }
    /**
    * @brief 获取支吊架模板
    */
    inline WD::WDSupport::SharedPtr support()
    {
        return _pSupport.lock();
    }

protected:
    virtual void showEvent(QShowEvent* evt) override;
    virtual void hideEvent(QHideEvent* evt) override;

signals:
    void sigSupportParamEditFinished();

private slots:
    /*
    * brief 添加支吊架参数因子
    */
    void slotAddFactor();
    /*
    * brief 移除支吊架参数因子
    */
    void slotRemoveFactor();
    /*
    * brief 保存支吊架模板数据
    */
    void slotUpdateParamFactor();
    /*
    * brief 更新结构数据
    */
    void slotUpdateStructParams();
    /*
    * brief 支吊架结构树节点切换
    */
    void slotSupportStructItemChanged();
    /*
    * brief 确认按钮按下通知响应
    */
    void slotOKClicked();
    /*
    * brief path按钮按下通知响应
    */
    void slotPathClicked();

private:
    /*
    * brief 初始化结构参数表
    */
    void initStructTree();
    /*
    * brief 初始化结构参数表
    */
    void initStructParamTable();
    /*
    * brief 初始化参数因子表
    */
    void initParamFactorTable();

    /*
    * brief 判断节点是否可以作为模板参数引用节点
    * param pNode 节点
    */
    bool enableSupportParamRef(WD::WDNode* pNode);
    /*
    * brief 创建项
    * param pNode 节点
    */
    void addItem(WD::WDNode* pParentNode, QTreeWidgetItem* pParentItem);

    /*
    * brief 更新支吊架结构树
    * param pSupport 支吊架模板
    */
    void updateSupportStruct();
    /*
    * brief 更新结构参数表
    * param pNode 结构节点
    */
    void updateStructParams(WD::WDNode::SharedPtr pNode);
    /*
    * brief 更新支吊架模板参数因子
    */
    void updateSupportFactors();
    /*
    * brief 更新支吊架等级
    */
    void updateSpecList();
    /*
    * brief 更新通用类型
    */
    void updateGTypeList();
    /*
    * brief 更新描述
    */
    void updateDesc();
    /*
    * brief 更新图例
    */
    void updateLegend();

private:
    /**
     * @brief 将SCTN的属性添加到属性组
     * @param group 属性组
     * @param node SCTN节点
    */
    void sctnPropertyToGroup(WD::WDPropertyGroup& group, WD::WDNode& node);

private:
    /*
    * brief 文本翻译
    */
    void translate();

private:
    Ui::SupportParamEdit    ui;
    WD::WDCore&             _core;
    // 支吊架模板管理
    WD::WDSupportMgr&        _supportMgr;
    // 支吊架模板
    WD::WDSupport::WeakPtr   _pSupport;
    // 当前支吊架参数引用节点
    WD::WDNode::WeakPtr     _pCurNode;
    // 支吊架参数临时数据
    TempData                _tempData;
};
