#pragma once

#include    "../../wizDesignerApp/UiInterface/UiInterface.h"
#include    "WDCore.h"
#include    "nodeTree/WDNodeTree.h"
#include    "extension/WDPluginToolSet.h"
#include    "core/math/DirectionParser.h"
#include    "core/viewer/WDViewer.h"
#include    "extension/WDPluginFormat.h"
#include    "core/common/WDTask.h"
#include    "NodeExportDialog.h"
#include    "core/common/WDConfig.h"
#include    <QTimer>

class UIComNodeFileFunction
    : public QObject
    , public IUiComponent
{
public:
    using   Formats = WD::WDPluginFormat::Formats;
    using   MapString = std::map<std::string, std::string>;
protected:

    Q_OBJECT
private:
    WD::WDCore& _core;
    NodeExportDialog _exportWidget;

    // 轮询保存timer
    QTimer* _timer;
    bool    _autoSave;
    int     _autoSaveSpacing;
public:
    UIComNodeFileFunction(IMainWindow& mainWindow, const UiComponentAttributes& attrs);

    virtual ~UIComNodeFileFunction();

    /**
    *   @brief 通知事件
    *   @param pNotice 事件对象
    */
    virtual void onNotice(UiNotice* pNotice) override;
    /**
    *   @brief 获取组件中UI对象
    *   @param  name: 一个组件可能有多个窗口，根据名称区分,name == nullpr,返回默认窗口
    */
    virtual QWidget* getWidget(const char* name) override;

Q_SIGNALS:
    void    finishLoad(WD::WDTask::SharedPtr);
    void    progress(float p, QString);
public slots:
    void    slotFinishLoad(WD::WDTask::SharedPtr);
    void    slotProgress(float p, QString);
    /**
     * @brief 轮询通知 响应
    */
    void    slotRepeatSave();

private:
    /**
     * @brief 配置项(自动保存)值更改通知响应
     * @param item 配置项
    */
    void    onCfgAutoSaveValueChanged(const WD::WDConfigItem& item);
    /**
     * @brief 配置项(自动保存时间间隔)值更改通知响应
     * @param item 配置项
    */
    void    onCfgAutoSaveSpacingValueChanged(const WD::WDConfigItem& item);

private:
    bool onAction(UiActionNotice* pActionNotice);
    // 设计界面XML导入
    void onActionDesignImport();
    // 元件库界面xml导入
    void onActionCatelogImport();
    // 设计界面XML导出
    void onActionDesignExport();
    // 元件库界面xml导出
    void onActionCatelogExport();
    /**
     * @brief 获取读/写文件选择对话框筛选器格式的字符串
     * @param rw 读/写
    */
    std::string getFilterStr(const WD::WDPluginFormat::FormatAttr& rw);
    /**
     * @brief 获取读/写文件版本
     * @param rw 读/写
    */
    MapString   getFmtVersion(const WD::WDPluginFormat::FormatAttr& rw);

private:
    /**
     * @brief 保存MD5
    */
    void    saveMd5(std::string_view bmName);

private:
    /**
     * @brief 保存到本地
    */
    bool    saveToLocal();

private:
    /**
     * @brief 准备任务回调
    */
    void    prepareTaskCallbacks(WD::WDTask& task);
};