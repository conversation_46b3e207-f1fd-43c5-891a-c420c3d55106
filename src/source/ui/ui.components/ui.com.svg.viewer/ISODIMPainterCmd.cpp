#include "ISODIMPainterCmd.h"


WD_NAMESPACE_BEGIN

ISODIMPainterCmd::TextSizeCalculator::TextSizeCalculator(const ISOUnitConvert& uCvt
    , const std::string_view& text
    , const WDDIMFontStyle& style)
{
    int fontSz      = Max(1, static_cast<int>(uCvt.worldToPixel(style.fontSize)));
    int borderWidth = Max(1, static_cast<int>(uCvt.worldToPixel(style.bolderWidth)));

    font.setFamily(QString::fromUtf8(style.famliy.c_str()));
    font.setPixelSize(fontSz);

    QString str = QString::fromUtf8(text.data());
    QFontMetrics fontM(font);
    int w = fontM.horizontalAdvance(str);
    int h = fontM.height();
    auto tightRect = fontM.tightBoundingRect(str);
    // 紧裹着文字像素的宽高
    this->size = DVec2(w, h);
    this->tightSize = DVec2(tightRect.width(), tightRect.height());
    // 边框宽高
    this->borderSize = DVec2::Zero();
    this->borderPosOffset = DVec2::Zero();
    if (style.bolderType != WDDIMFontStyle::BolderType::BT_None) // 这里 +2.0f 是将边框整体放大1像素，方式边框与文字边缘重合,影响美观
    {
        this->borderSize        = this->size + DVec2(borderWidth);
        this->borderPosOffset   = DVec2(- borderWidth * 0.5, borderWidth * 0.5 + 1.0);
    }
    // 
    this->halfSize          = this->size * 0.5;
    this->halfTightSize     = this->tightSize * 0.5;
    this->halfBorderSize    = this->borderSize * 0.5;
    // 计算一个最大尺寸
    this->maxSize           = DVec2::Max(this->size, this->borderSize);
    this->halfMaxSize       = this->maxSize * 0.5;
}

double ISODIMPainterCmd::TextSizeCalculator::RotateAngle(const DVec2& vec)
{
    // 这里使用屏幕坐标构造一个坐标系, up为屏幕上方向:-Y, right为屏幕右方向:X, front使用up和right计算得到
    static const auto right = DVec3::AxisX();
    static const auto up    = DVec3::AxisNY();
    static const auto front = DVec3::Cross(up, right).normalized();

    DVec3 tRight = DVec3(vec.normalized());
    auto tmpDot = DVec3::Dot(tRight, up);
    if (Abs(1.0 - Abs(tmpDot)) <= 0.01)
    {
        tRight = up;
    }
    else
    {
        DVec3 tFront = DVec3::Cross(up, tRight);
        if (DVec3::Dot(tFront, front) < 0.0)
            tRight = -tRight;
    }
    double rAngle = DVec3::Angle(right, tRight);
    if (DVec3::Dot(tRight, up) > 0.0)
        rAngle = -rAngle;

    return rAngle;
}

ISODIMPainterCmd::ISODIMPainterCmd(const ISOUnitConvert& cvt)
    : _cvt(cvt)
{
}

ISODIMPainterCmd::~ISODIMPainterCmd()
{
}

DVec2 ISODIMPainterCmd::calcTextSize(const std::string& text
    , const WDDIMFontStyle& style)
{
    if (text.empty())
    {
        assert(false);
        return DVec2::Zero();
    }
    TextSizeCalculator tc(_cvt, text, style);
    return _cvt.pixelToWorld(tc.maxSize);
}


void ISODIMPainterCmd::drawPoints(const std::vector<DVec3>& points
    , const WDDIMPointStyle& style)
{
    _cmds.push_back(Cmd([points, style](WDDIMPainter& painter)
        {
            painter.drawPoints(points, style);
        }));
}

void ISODIMPainterCmd::drawLine(const DVec3& sPos
    , const DVec3& ePos
    , const WDDIMLineStyle& style)
{
    _cmds.push_back(Cmd([sPos, ePos, style](WDDIMPainter& painter)
        {
            painter.drawLine(sPos, ePos, style);
        }));
}

void ISODIMPainterCmd::drawLines(const std::vector<DVec3>& points
    , const WDDIMLineStyle& style)
{
    _cmds.push_back(Cmd([points, style](WDDIMPainter& painter)
        {
            painter.drawLines(points, style);
        }));
}

void ISODIMPainterCmd::drawBrokenLine(const std::vector<DVec3>& points
    , const WDDIMLineStyle& style, WD::Color fillColor)
{
    _cmds.push_back(Cmd([points, style, fillColor](WDDIMPainter& painter)
        {
            painter.drawBrokenLine(points, style, fillColor);
        }));
}

void ISODIMPainterCmd::drawLoopLine(const std::vector<DVec3>& points
    , const WDDIMLineStyle& style, WD::Color fillColor)
{
    _cmds.push_back(Cmd([points, style, fillColor](WDDIMPainter& painter)
        {
            painter.drawLoopLine(points, style, fillColor);
        }));
}

void ISODIMPainterCmd::drawLoopLine2D(const std::vector<DVec2>& points, const WDDIMLineStyle& style, WD::Color fillColor)
{
    _cmds.push_back(Cmd([points, style, fillColor](WDDIMPainter& painter)
        {
            painter.drawLoopLine2D(points, style, fillColor);
        }));
}

void ISODIMPainterCmd::drawText2D(const std::string& text
    , const DVec2& position
    , const WDDIMFontStyle& style
    , const WDDIMAlign& textAlign)
{
    _cmds.push_back(Cmd([text, position, style, textAlign](WDDIMPainter& painter)
        {
            painter.drawText2D(text, position, style, textAlign);
        }));
}

void ISODIMPainterCmd::drawArc(const DVec3& center
    , double radius
    , const DVec3& sDirection
    , const DVec3& eDirection
    , const WDDIMLineStyle& style)
{
    _cmds.push_back(Cmd([center, radius, sDirection, eDirection, style](WDDIMPainter& painter)
        {
            painter.drawArc(center, radius, sDirection, eDirection, style);
        }));
}

void ISODIMPainterCmd::drawRect(const DVec3& center
    , const DVec2& size
    , const DVec3& xAxis
    , const DVec3& planeNormal
    , const WDDIMLineStyle& style)
{
    _cmds.push_back(Cmd([center, size, xAxis, planeNormal, style](WDDIMPainter& painter)
        {
            painter.drawRect(center, size, xAxis, planeNormal, style);
        }));
}

void ISODIMPainterCmd::fillRect(const DVec3& center
    , const DVec2& size
    , const DVec3& xAxis
    , const DVec3& planeNormal
    , const WDDIMShapeFillStyle& style)
{
    _cmds.push_back(Cmd([center, size, xAxis, planeNormal, style](WDDIMPainter& painter)
        {
            painter.fillRect(center, size, xAxis, planeNormal, style);
        }));
}

void ISODIMPainterCmd::drawCircle(const DVec3& center
    , double radius
    , const DVec3& planeNormal
    , const WDDIMLineStyle& style)
{
    _cmds.push_back(Cmd([center, radius, planeNormal, style](WDDIMPainter& painter)
        {
            painter.drawCircle(center, radius, planeNormal, style);
        }));
}

void ISODIMPainterCmd::fillCircle(const DVec3& center
    , double radius
    , const DVec3& planeNormal
    , const WDDIMShapeFillStyle& style
    , const std::optional<DVec3>& xAxis)
{
    _cmds.push_back(Cmd([center, radius, planeNormal, style, xAxis](WDDIMPainter& painter)
        {
            painter.fillCircle(center, radius, planeNormal, style, xAxis);
        }));
}

void ISODIMPainterCmd::drawTriangle(const std::array<DVec3, 3>& vertices
    , const WDDIMLineStyle& style)
{
    _cmds.push_back(Cmd([vertices, style](WDDIMPainter& painter)
        {
            painter.drawTriangle(vertices, style);
        }));
}

void ISODIMPainterCmd::fillTriangle(const std::array<DVec3, 3>& vertices
    , const WDDIMShapeFillStyle& style
    , const std::optional<DVec3>& xAxis)
{
    _cmds.push_back(Cmd([vertices, style, xAxis](WDDIMPainter& painter)
        {
            painter.fillTriangle(vertices, style, xAxis);
        }));
}

DVec2 ISODIMPainterCmd::drawText(const std::string& text
    , const DVec3& position
    , const DVec3& rightDir
    , const DVec3& upDir
    , const WDDIMFontStyle& style
    , const WDDIMAlign& textAlign)
{
    _cmds.push_back(Cmd([text, position, rightDir, upDir, style, textAlign](WDDIMPainter& painter)
        {
            painter.drawText(text, position, rightDir, upDir, style, textAlign);
        }));

    // 测量文字尺寸
    return this->calcTextSize(text, style);
}

DVec2 ISODIMPainterCmd::drawTexts(const DVec3& pos
    , const DVec3& rightDir
    , const DVec3& upDir
    , std::vector<std::pair<std::string, WDDIMFontStyle> > texts
    , WDDIMAlign align
    , WDDIMAlign::HAlign rowAlign)
{
    _cmds.push_back(Cmd([pos, rightDir, upDir, texts, align, rowAlign](WDDIMPainter& painter)
        {
            painter.drawTexts(pos, rightDir, upDir, texts, align, rowAlign);
        }));
    // 所有文本的宽高
    DVec2 allSz = DVec2::Zero();
    for (const auto& t : texts)
    {
        DVec2 sz = this->calcTextSize(t.first, t.second);
        // 宽度取最大值
        allSz.x = Max(allSz.x, sz.x);
        // 高度累加
        allSz.y += sz.y;
    }
    return allSz;
}

void ISODIMPainterCmd::repaintToPainter(WDDIMPainter& painter)
{
    if (_cmds.empty())
        return;
    for (const auto& cmd : _cmds)
    {
        cmd.repaint(painter);
    }
}

WD_NAMESPACE_END

