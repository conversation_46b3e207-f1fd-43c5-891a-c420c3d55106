#include "ISOPipingGenerate.h"
#include "core/businessModule/catalog/spec/WDBMCSpecSPCO.h"
#include "ISOTableWidget.h"

#include "core/businessModule/design/pipeWork/WDBMDPipeComsSub.h"
#include "core/businessModule/catalog/cata/component/WDBMCCataComponent.h"
#include "core/businessModule/catalog/cata/element/WDBMCCataTEXT.h"
#include "core/businessModule/catalog/cata/element/WDBMCCataSDTE.h"
#include "core/businessModule/catalog/cmpw/WDBMCCmpwTUBD.h"
#include "core/businessModule/catalog/cmpw/WDBMCCmpwCMPD.h"
#include "core/businessModule/catalog/cata/component/WDBMCCataSCOM.h"
#include "core/businessModule/catalog/cata/se/WDBMCCataBTSE.h"
#include "core/businessModule/catalog/cata/element/WDBMCCataBLTP.h"
#include "core/businessModule/catalog/bolt/WDBMCBoltSBOL.h"
#include "WDCore.h"
#include <QDateTime>
#include <regex>

WD_NAMESPACE_BEGIN

// ��������ͳ����
class MaterialDataStatistics
{
public:
    struct TypeInfo
    {
        // ��¼��ͬ�ڵ�(�������ָͬ�������õ�ͬһ��Ԫ��)������
        int typeCnt = 0;
        // ����ڵ������ֵ
        std::map<ISOMaterialArea::MaterialPaintFlag, std::string> nodeAttrs;
        // ��¼����
        double weight = 0.0;
        // ��¼����
        double allWeight = 0.0;
    };
    const ISOMaterialArea& materialTable;
    //      <���, ������Ϣ> ��������map, һ�����������ϱ������,һ���ǰ�װ���ϱ������
    std::map<int, TypeInfo> typeFabricationMap;
    std::map<int, TypeInfo> typeErectionMap;
public:
    MaterialDataStatistics(const ISOMaterialArea& materialTable) : materialTable(materialTable){}
    ~MaterialDataStatistics(){}
public:
    bool execComNode(const TuComNode& pTuComNode)
    {
        auto pNode = pTuComNode.comNode();
        if (pNode == nullptr)
            return false;
        auto pData = pNode->getBDSub<WDBMDPipeCOMS>();
        if (pData == nullptr)
            return false;
        auto pSpcoNode = pData->spRef().refNode();
        if (pSpcoNode == nullptr)
            return false;
        TypeInfo* typeInfo = nullptr;
        auto& number = pTuComNode.number;
        if (pData->shop)
        {
            auto itr = typeFabricationMap.find(number);
            if (itr == typeFabricationMap.end())
            {
                auto res = typeFabricationMap.emplace(number, TypeInfo());
                if (!res.second)
                    return false;
                itr = res.first;
            }
            typeInfo = &(itr->second);
        }
        else
        {
            auto itr = typeErectionMap.find(number);
            if (itr == typeErectionMap.end())
            {
                auto res = typeErectionMap.emplace(number, TypeInfo());
                if (!res.second)
                    return false;
                itr = res.first;
            }
            typeInfo = &(itr->second);
        }
        assert(typeInfo != nullptr);
        if (typeInfo == nullptr)
            return false;
        typeInfo->typeCnt++;
        MaterialDataCommon(*pSpcoNode, number, *typeInfo, materialTable, pNode->type());
        // �����ͷ���Ҫͳ����˨������    (��˨�����ݲ�ʵ��)
#if 0
        if (pNode->isAnyOfType(WDBMDPipeVALV::Type, WDBMDPipeFLAN::Type))
        {
            if (_pPreNode == nullptr)
            {
                _pPreNode = pNode;
                return true;
            }
            auto pPreData = _pPreNode->getBDSub<WDBMDPipeCOMS>();
            if (pPreData == nullptr)
            {
                _pPreNode = nullptr;
                return false;
            }
            auto pPreSpcoNode = pPreData->spRef().refNode();
            if (pPreSpcoNode == nullptr)
            {
                _pPreNode = nullptr;
                return false;
            }
            // ��һ������/�����ڵ��Ԫ��
            auto pPreSpcoData = pPreSpcoNode->getBDSub<WDBMCSpecSPCO>();
            if (pPreSpcoData == nullptr)
            {
                _pPreNode = nullptr;
                return false;
            }
            // ��ǰ����/�����ڵ��Ԫ��
            auto pSpcoData = pSpcoNode->getBDSub<WDBMCSpecSPCO>();
            if (pSpcoData == nullptr)
            {
                _pPreNode = nullptr;
                return false;
            }
            auto preBType = GetSpcoNodeBType(*pPreSpcoData);
            auto bType = GetSpcoNodeBType(*pSpcoData);

            auto bDia = GetSpcoNodeBDia(*pPreSpcoData);
            WDNode* pParent = pSpcoNode->parent();
            WDNode::SharedPtr pSeleNode = nullptr;
            while (pParent != nullptr)
            {
                if (!WD::WDBMCSpecSPWL::Is(*pParent))
                {
                    pParent = pParent->parent();
                    continue;
                }
                for (auto& pSpec : pParent->children())
                {
                    if (pSpec == nullptr)
                        continue;
                    auto pSpecData = pSpec->getBDSub<WDBMCSpecSPEC>();
                    if (pSpecData == nullptr || !pSpecData->purpose().empty())
                        continue;
                    for (auto& pSeleChild : pSpec->children())
                    {
                        if (pSeleChild == nullptr || !WDBMCSpecSELE::Is(*pSeleChild))
                            continue;
                        auto value = Core().catalogMgr().getAttribute(*pSeleChild, "Tanswer");
                        std::string* tAnswer = value.data<std::string>();
                        if (tAnswer == nullptr || *tAnswer != "BOLT")
                            continue;
                        for (auto& pBoreChild : pSeleChild->children())
                        {
                            if (pBoreChild == nullptr || !WDBMCSpecSELE::Is(*pBoreChild))
                                continue;
                            value = Core().catalogMgr().getAttribute(*pBoreChild, "Answer");
                            auto answer = value.data<std::string>();
                            if (answer == nullptr || *answer != bDia)
                                continue;
                            pSeleNode = pBoreChild;
                            break;
                        }
                        if (pSeleNode != nullptr)
                            break;
                    }
                    if (pSeleNode != nullptr)
                        break;
                }
                break;
            }
            if (pSeleNode == nullptr)
            {
                _pPreNode = nullptr;
                return false;
            }
            struct HandleType
            {
                // ��ˮ��
                int number;
                // ������
                bool (*func)(const WDBMCSpecSPCO&, const WDNode&, int, TypeInfo&, const ISOMaterialArea&);
                // tAnswer��ֵ
                std::string bType;
                // bSel��ֵ
                std::string bSele;
            };
            using HandleTypes = std::vector<HandleType>;
            HandleTypes types;
            if ((preBType == "STUD" && bType == "STUD")
                || (preBType == "STUD" && (bType == "BOLT" || bType.empty()))
                || (preBType == "BOLT" || preBType.empty()) && bType == "STUD")
            {
                types.push_back({pTuComNode.boltNumber, MaterialDataStatistics::ExecBoltSpco, "STUD", "W"});
                types.push_back({pTuComNode.nutNumber, MaterialDataStatistics::ExecNutSpco, "NUT", "W"});
                types.push_back({pTuComNode.gasketNumber, MaterialDataStatistics::ExecWashSpco, "WASH", "W"});
            }
            else if ((preBType == "CAP" && bType == "CAP")
                || (preBType == "CAP" && (bType == "BOLT" || bType.empty()))
                || ((preBType == "BOLT" || preBType.empty()) && bType == "CAP"))
            {
                types.push_back({pTuComNode.boltNumber, MaterialDataStatistics::ExecBoltSpco, "CAP", "A"});
                types.push_back({pTuComNode.nutNumber, MaterialDataStatistics::ExecNutSpco, "NUT", "A"});
            }
            else if ((preBType == "BOLT" || preBType.empty()) && (bType == "BOLT" || bType.empty()))
            {
                types.push_back({pTuComNode.boltNumber, MaterialDataStatistics::ExecBoltSpco, "STUD", "W"});
                types.push_back({pTuComNode.nutNumber, MaterialDataStatistics::ExecNutSpco, "NUT", "W"});
                types.push_back({pTuComNode.gasketNumber, MaterialDataStatistics::ExecWashSpco, "WASH", "W"});
            }
            for (auto& type : types)
            {
                for (auto& pSeleChild : pSeleNode->children())
                {
                    if (!WDBMCSpecSELE::Is(*pSeleChild))
                        continue;
                    auto value = Core().catalogMgr().getAttribute(*pSeleChild, "Tanswer");
                    auto tAnswer = value.data<std::string>();
                    if (tAnswer == nullptr || *tAnswer != type.bType)
                        continue;
                    for (auto& pSpco : pSeleChild->children())
                    {
                        if (!WDBMCSpecSPCO::Is(*pSpco))
                            continue;
                        value = Core().catalogMgr().getAttribute(*pSpco, "Tanswer");
                        tAnswer = value.data<std::string>();
                        if (tAnswer == nullptr || tAnswer->empty())
                            continue;
                        if (*tAnswer == type.bSele)
                        {
                            pSpcoNode = pSpco;
                            break;
                        }
                    }
                    // �����������Ϊ��ʶ
                    auto itr = typeErectionMap.find(type.number);
                    if (itr == typeErectionMap.end())
                    {
                        auto res = typeErectionMap.emplace(type.number, TypeInfo());
                        if (!res.second)
                        {
                            _pPreNode = nullptr;
                            return false;
                        }
                        itr = res.first;
                    }
                    itr->second.typeCnt++;
                    // ����SPCO�ڵ�
                    type.func(*pPreSpcoData, *pSpcoNode, type.number, itr->second, materialTable);
                    break;
                }
            }
            _pPreNode = nullptr;
        }
#endif
        return true;
    }
    bool execTubiNode(const TuTubiNode& pTuComNode, double& tubeLength)
    {
        auto pNode = pTuComNode.tubiNode();
        if (pNode == nullptr)
            return false;
        auto pData = pNode->getBDSub<WDBMDPipeTUBI>();
        if (pData == nullptr)
            return false;
        auto& nodeRef = pData->spRef();
        if (nodeRef.empty())
            return false;
        auto pSpcoNode = nodeRef.refNode();
        if (pSpcoNode == nullptr)
            return false;
        TypeInfo* typeInfo = nullptr;
        auto& number = pTuComNode.number;
        auto mapItr = typeFabricationMap.find(number);
        if (mapItr == typeFabricationMap.end())
        {
            auto res = typeFabricationMap.emplace(number, TypeInfo());
            if (!res.second)
                return false;
            mapItr = res.first;
        }
        typeInfo = &(mapItr->second);
        typeInfo->typeCnt++;

        auto& flags = materialTable.flags;
        auto& attrs = typeInfo->nodeAttrs;
        // ����
        if (flags.hasFlag(ISOMaterialArea::MPF_DanZhong))
        {
            auto pSpcoData = pSpcoNode->getBDSub<WDBMCSpecSPCO>();
            if (pSpcoData == nullptr)
                return false;
            auto itr = attrs.find(ISOMaterialArea::MPF_DanZhong);
            if (itr == attrs.end())
            {
                char value[1024] = { 0 };
                auto pRefNode = pSpcoData->cmpRef().refNode();
                if (pRefNode != nullptr)
                {
                    bool bOk = false;
                    double doubleVar = 0.0;
                    auto pTubd = pRefNode->getBDSub<WDBMCCmpwTUBD>();
                    if (pTubd != nullptr)
                        doubleVar = FromString<double>(pTubd->uweight, &bOk);
                    if (bOk)
                    {
                        typeInfo->weight = doubleVar;
                        // +0.005��Ϊ����������
                        sprintf_s(value, sizeof(value), "%f", typeInfo->weight);
                    }
                }
                attrs.emplace(ISOMaterialArea::MPF_DanZhong, std::string(value));
            }
            else if (itr->second.empty())
            {
                auto pRefNode = pSpcoData->cmpRef().refNode();
                if (pRefNode != nullptr)
                {
                    bool bOk = false;
                    double doubleVar = 0.0;
                    auto pTubd = pRefNode->getBDSub<WDBMCCmpwTUBD>();
                    if (pTubd != nullptr)
                        doubleVar = FromString<double>(pTubd->uweight, &bOk);
                    if (bOk)
                    {
                        typeInfo->weight = doubleVar;
                        // +0.005��Ϊ����������
                        char value[1024] = { 0 };
                        sprintf_s(value, sizeof(value), "%.2fkg/mm", typeInfo->weight + 0.005);
                        itr->second = std::string(value);
                    }
                }
            }
        }
        MaterialDataCommon(*pSpcoNode, number, *typeInfo, materialTable, pNode->type());
        // ���� (�ܵ��ĳ���)
        if (flags.hasFlag(ISOMaterialArea::MPF_ShuLiang))
        {
            char value[1024] = { 0 };
            auto pTubi = pNode->getBDSub<WDBMDPipeTUBI>();
            tubeLength += pTubi->length();
            // tubeLength�������1000��Ϊ�˽���λ��mmתΪm , +0.005��Ϊ����������
            sprintf_s(value, sizeof(value), "%.2fm", tubeLength / 1000 + 0.005);
            auto itr = attrs.find(ISOMaterialArea::MPF_ShuLiang);
            if (itr == attrs.end())
                attrs.emplace(ISOMaterialArea::MPF_ShuLiang, value);
            else
                itr->second = value;
        }
        // ����
        if (flags.hasFlag(ISOMaterialArea::MPF_ZongZhong))
        {
            char value[1024] = { 0 };
            typeInfo->allWeight = typeInfo->weight * tubeLength;
            if (typeInfo->allWeight > NumLimits<double>::Epsilon)
            {
                // +0.005��Ϊ����������
                sprintf_s(value, sizeof(value), "%.2f", typeInfo->allWeight + 0.005);
            }
            auto itr = attrs.find(ISOMaterialArea::MPF_ZongZhong);
            if (itr == attrs.end())
                attrs.emplace(ISOMaterialArea::MPF_ZongZhong, value);
            else
                itr->second = value;
        }
        return true;
    }
    // ͳ����˨����
    static bool ExecBoltSpco(const WDBMCSpecSPCO& flanSpcoNode, const WDNode& boltSpcoNode, int number, TypeInfo& typeInfo, const ISOMaterialArea& materialTable)
    {
        auto pSpcoData = boltSpcoNode.getBDSub<WDBMCSpecSPCO>();
        if (pSpcoData == nullptr)
            return false;
        auto& flags = materialTable.flags;
        auto& attrs = typeInfo.nodeAttrs;
        // ���
        if (flags.hasFlag(ISOMaterialArea::MPF_GuiGe))
        {
            auto itr = attrs.find(ISOMaterialArea::MPF_GuiGe);
            if (itr == attrs.end())
                attrs.emplace(ISOMaterialArea::MPF_GuiGe, GetSpcoNodeBDia(flanSpcoNode));
            else if (itr->second.empty())
                itr->second = GetSpcoNodeBDia(flanSpcoNode);
        }
        // ���ϴ���
        if (flags.hasFlag(ISOMaterialArea::MPF_WuLiaoDaiMa))
        {
            auto itr = attrs.find(ISOMaterialArea::MPF_WuLiaoDaiMa);
            if (itr == attrs.end())
            {
                std::string value = GetNodeWuLiaoDaiMa(boltSpcoNode);
                std::string length = GetBoltLength(*pSpcoData);
                if (!value.empty() && !length.empty())
                    attrs.emplace(ISOMaterialArea::MPF_WuLiaoDaiMa, value + " " + length);
                else
                    attrs.emplace(ISOMaterialArea::MPF_WuLiaoDaiMa, "");
            }
            else if (itr->second.empty())
            {
                std::string value = GetNodeWuLiaoDaiMa(boltSpcoNode);
                std::string length = GetBoltLength(*pSpcoData);
                if (!value.empty() && !length.empty())
                    itr->second = value + " " + length;
            }
        }
        // ����
        if (flags.hasFlag(ISOMaterialArea::MPF_DanZhong))
        {
            auto itr = attrs.find(ISOMaterialArea::MPF_DanZhong);
            if (itr == attrs.end())
            {
                char value[1024] = { 0 };
                auto pRefNode = pSpcoData->cmpRef().refNode();
                if (pRefNode != nullptr)
                {
                    bool bOk = false;
                    auto pCmpd = pRefNode->getBDSub<WDBMCCmpwCMPD>();
                    typeInfo.weight = FromString<double>(pCmpd->cweight, &bOk);
                    if (bOk)
                    {
                        // +0.005��Ϊ����������
                        sprintf_s(value, sizeof(value), "%.2f", typeInfo.weight + 0.005);
                    }
                }
                attrs.emplace(ISOMaterialArea::MPF_DanZhong, std::string(value));
            }
            else if (itr->second.empty())
            {
                auto pRefNode = pSpcoData->cmpRef().refNode();
                if (pRefNode != nullptr)
                {
                    bool bOk = false;
                    auto pCmpd = pRefNode->getBDSub<WDBMCCmpwCMPD>();
                    typeInfo.weight = FromString<double>(pCmpd->cweight, &bOk);
                    if (bOk)
                    {
                        // +0.005��Ϊ����������
                        char value[1024] = { 0 };
                        sprintf_s(value, sizeof(value), "%.2f", typeInfo.weight + 0.005);
                        itr->second = std::string(value);
                    }
                }
            }
        }
        MaterialDataCommon(boltSpcoNode, number, typeInfo, materialTable);
        // ����
        int cnt = 0;
        if (flags.hasFlag(ISOMaterialArea::MPF_ShuLiang))
        {
            auto pBoltNode = pSpcoData->bltRef().refNode();
            if (pBoltNode != nullptr)
            {
                auto pBoltData = pBoltNode->getBDSub<WDBMCBoltSBOL>();
                if (pBoltData != nullptr)
                    cnt = pBoltData->noff;
            }
            auto itr = attrs.find(ISOMaterialArea::MPF_ShuLiang);
            if (itr == attrs.end())
                attrs.emplace(ISOMaterialArea::MPF_ShuLiang, ToString<int>(cnt));
            else
                itr->second = ToString<int>(cnt);
        }
        // ����
        if (flags.hasFlag(ISOMaterialArea::MPF_ZongZhong))
        {
            char value[1024] = { 0 };
            typeInfo.allWeight = typeInfo.weight * cnt;
            if (typeInfo.allWeight > NumLimits<double>::Epsilon)
            {
                // +0.005��Ϊ����������
                sprintf_s(value, sizeof(value), "%.2f", typeInfo.allWeight + 0.005);
            }
            auto itr = attrs.find(ISOMaterialArea::MPF_ZongZhong);
            if (itr == attrs.end())
                attrs.emplace(ISOMaterialArea::MPF_ZongZhong, value);
            else
                itr->second = value;
        }
        return true;
    }
    // ͳ����ĸ����
    static bool ExecNutSpco(const WDBMCSpecSPCO& flanSpcoNode, const WDNode& boltSpcoNode, int number, TypeInfo& typeInfo, const ISOMaterialArea& materialTable)
    {
        auto pSpcoData = boltSpcoNode.getBDSub<WDBMCSpecSPCO>();
        if (pSpcoData == nullptr)
            return false;
        auto& flags = materialTable.flags;
        auto& attrs = typeInfo.nodeAttrs;
        // ���
        if (flags.hasFlag(ISOMaterialArea::MPF_GuiGe))
        {
            auto itr = attrs.find(ISOMaterialArea::MPF_GuiGe);
            if (itr == attrs.end())
                attrs.emplace(ISOMaterialArea::MPF_GuiGe, GetSpcoNodeBDia(flanSpcoNode));
            else if (itr->second.empty())
                itr->second = GetSpcoNodeBDia(flanSpcoNode);
        }
        MaterialDataCommon(boltSpcoNode, number, typeInfo, materialTable);
        int cnt = 0;
        // ����
        if (flags.hasFlag(ISOMaterialArea::MPF_ShuLiang))
        {
            auto pBoltNode = pSpcoData->bltRef().refNode();
            if (pBoltNode != nullptr)
            {
                auto pBoltData = pBoltNode->getBDSub<WDBMCBoltSBOL>();
                if (pBoltData != nullptr)
                    for (auto& each : pBoltData->bItems)
                        if (each == "NUT")
                            cnt++;
            }
            auto itr = attrs.find(ISOMaterialArea::MPF_ShuLiang);
            if (itr == attrs.end())
                attrs.emplace(ISOMaterialArea::MPF_ShuLiang, ToString<int>(cnt));
            else
                itr->second = ToString<int>(cnt);
        }
        // ����
        if (flags.hasFlag(ISOMaterialArea::MPF_ZongZhong))
        {
            char value[1024] = { 0 };
            typeInfo.allWeight = typeInfo.weight * cnt;
            if (typeInfo.allWeight > NumLimits<double>::Epsilon)
            {
                // +0.005��Ϊ����������
                sprintf_s(value, sizeof(value), "%.2f", typeInfo.allWeight + 0.005);
            }
            auto itr = attrs.find(ISOMaterialArea::MPF_ZongZhong);
            if (itr == attrs.end())
                attrs.emplace(ISOMaterialArea::MPF_ZongZhong, value);
            else
                itr->second = value;
        }
        return true;
    }
    // ͳ�Ƶ�Ƭ����
    static bool ExecWashSpco(const WDBMCSpecSPCO& flanSpcoNode, const WDNode& boltSpcoNode, int number, TypeInfo& typeInfo, const ISOMaterialArea& materialTable)
    {
        auto pSpcoData = boltSpcoNode.getBDSub<WDBMCSpecSPCO>();
        if (pSpcoData == nullptr)
            return false;
        auto& flags = materialTable.flags;
        auto& attrs = typeInfo.nodeAttrs;
        // ���
        if (flags.hasFlag(ISOMaterialArea::MPF_GuiGe))
        {
            auto itr = attrs.find(ISOMaterialArea::MPF_GuiGe);
            if (itr == attrs.end())
                attrs.emplace(ISOMaterialArea::MPF_GuiGe, GetSpcoNodeBDia(flanSpcoNode));
            else if (itr->second.empty())
                itr->second = GetSpcoNodeBDia(flanSpcoNode);
        }
        // ����
        if (flags.hasFlag(ISOMaterialArea::MPF_DanZhong))
        {
            auto itr = attrs.find(ISOMaterialArea::MPF_DanZhong);
            if (itr == attrs.end())
            {
                char value[1024] = { 0 };
                auto pRefNode = pSpcoData->cmpRef().refNode();
                if (pRefNode != nullptr)
                {
                    bool bOk = false;
                    auto pCmpd = pRefNode->getBDSub<WDBMCCmpwCMPD>();
                    typeInfo.weight = FromString<double>(pCmpd->cweight, &bOk);
                    if (bOk)
                    {
                        // +0.005��Ϊ����������
                        sprintf_s(value, sizeof(value), "%.2f", typeInfo.weight + 0.005);
                    }
                }
                attrs.emplace(ISOMaterialArea::MPF_DanZhong, std::string(value));
            }
            else if (itr->second.empty())
            {
                auto pRefNode = pSpcoData->cmpRef().refNode();
                if (pRefNode != nullptr)
                {
                    bool bOk = false;
                    auto pCmpd = pRefNode->getBDSub<WDBMCCmpwCMPD>();
                    typeInfo.weight = FromString<double>(pCmpd->cweight, &bOk);
                    if (bOk)
                    {
                        // +0.005��Ϊ����������
                        char value[1024] = { 0 };
                        sprintf_s(value, sizeof(value), "%.2f", typeInfo.weight + 0.005);
                        itr->second = std::string(value);
                    }
                }
            }
        }
        MaterialDataCommon(boltSpcoNode, number, typeInfo, materialTable);
        // ����
        int cnt = 0;
        if (flags.hasFlag(ISOMaterialArea::MPF_ShuLiang))
        {
            auto pBoltNode = pSpcoData->bltRef().refNode();
            if (pBoltNode != nullptr)
            {
                auto pBoltData = pBoltNode->getBDSub<WDBMCBoltSBOL>();
                if (pBoltData != nullptr)
                    for (auto& each : pBoltData->bItems)
                        if (each == "WASH")
                            cnt++;
            }
            auto itr = attrs.find(ISOMaterialArea::MPF_ShuLiang);
            if (itr == attrs.end())
                attrs.emplace(ISOMaterialArea::MPF_ShuLiang, ToString<int>(cnt));
            else
                itr->second = ToString<int>(cnt);
        }
        // ����
        if (flags.hasFlag(ISOMaterialArea::MPF_ZongZhong))
        {
            char value[1024] = { 0 };
            typeInfo.allWeight = typeInfo.weight * cnt;
            if (typeInfo.allWeight > NumLimits<double>::Epsilon)
            {
                // +0.005��Ϊ����������
                sprintf_s(value, sizeof(value), "%.2f", typeInfo.allWeight + 0.005);
            }
            auto itr = attrs.find(ISOMaterialArea::MPF_ZongZhong);
            if (itr == attrs.end())
                attrs.emplace(ISOMaterialArea::MPF_ZongZhong, value);
            else
                itr->second = value;
        }
        return true;
    }
    // ��������ͳ��(ͨ��)
    static bool MaterialDataCommon(const WDNode& spcoNode, int number, TypeInfo& typeInfo, const ISOMaterialArea& materialTable, const char* nodeType = nullptr)
    {
        auto pSpcoData = spcoNode.getBDSub<WDBMCSpecSPCO>();
        if (pSpcoData == nullptr)
            return false;
        auto& flags = materialTable.flags;
        auto& attrs = typeInfo.nodeAttrs;
        // ���
        if (flags.hasFlag(ISOMaterialArea::MPF_XuHao))
        {
            auto itr = attrs.find(ISOMaterialArea::MPF_XuHao);
            if (itr == attrs.end())
                attrs.emplace(ISOMaterialArea::MPF_XuHao, ToString<int>(number));
            else
                itr->second = ToString<int>(number);
        }

        // ������Ϣ
        if (flags.hasFlag(ISOMaterialArea::MPF_MiaoShu))
        {
            auto itr = attrs.find(ISOMaterialArea::MPF_MiaoShu);
            if (itr == attrs.end())
                attrs.emplace(ISOMaterialArea::MPF_MiaoShu, GetNodeMiaoShu(*pSpcoData, materialTable));
            else if (itr->second.empty())
                itr->second = GetNodeMiaoShu(*pSpcoData, materialTable);
        }

        // ���
        if (flags.hasFlag(ISOMaterialArea::MPF_GuiGe))
        {
            if (nodeType != nullptr)
            {
                auto itr = attrs.find(ISOMaterialArea::MPF_GuiGe);
                if (itr == attrs.end())
                    attrs.emplace(ISOMaterialArea::MPF_GuiGe, GetNodeGuiGe(*pSpcoData, nodeType));
                else if (itr->second.empty())
                    itr->second = GetNodeGuiGe(*pSpcoData, nodeType);
            }
        }

        // ���� (��δ֧��)
        if (flags.hasFlag(ISOMaterialArea::MPF_ChangDu))
        {
            auto itr = attrs.find(ISOMaterialArea::MPF_ChangDu);
            if (itr == attrs.end())
                attrs.emplace(ISOMaterialArea::MPF_ChangDu, "");
        }

        // ��� (��δ֧��)
        if (flags.hasFlag(ISOMaterialArea::MPF_KuanDu))
        {
            auto itr = attrs.find(ISOMaterialArea::MPF_KuanDu);
            if (itr == attrs.end())
                attrs.emplace(ISOMaterialArea::MPF_KuanDu, "");
        }

        // ����
        if (flags.hasFlag(ISOMaterialArea::MPF_CaiZhi))
        {
            auto itr = attrs.find(ISOMaterialArea::MPF_CaiZhi);
            if (itr == attrs.end())
            {
                std::string value;
                auto& matxtRef = pSpcoData->matxtRef();
                if (!matxtRef.empty())
                {
                    auto pRefNode = matxtRef.refNode();
                    auto pSmte = pRefNode->getBDSub<WDBMCCataSMTE>();
                    if (pSmte != nullptr)
                        value = pSmte->xText();
                }
                attrs.emplace(ISOMaterialArea::MPF_CaiZhi, value);
            }
            else if (itr->second.empty())
            {
                auto& matxtRef = pSpcoData->matxtRef();
                if (!matxtRef.empty())
                {
                    auto pRefNode = matxtRef.refNode();
                    auto pSmte = pRefNode->getBDSub<WDBMCCataSMTE>();
                    if (pSmte != nullptr)
                        itr->second = pSmte->xText();
                }
            }
        }

        // ���ϴ���
        if (flags.hasFlag(ISOMaterialArea::MPF_WuLiaoDaiMa))
        {
            auto itr = attrs.find(ISOMaterialArea::MPF_WuLiaoDaiMa);
            if (itr == attrs.end())
                attrs.emplace(ISOMaterialArea::MPF_WuLiaoDaiMa, GetNodeWuLiaoDaiMa(spcoNode));
            else if (itr->second.empty())
                itr->second = GetNodeWuLiaoDaiMa(spcoNode);
        }

        // ����
        if (flags.hasFlag(ISOMaterialArea::MPF_ShuLiang))
        {
            auto itr = attrs.find(ISOMaterialArea::MPF_ShuLiang);
            if (itr == attrs.end())
                attrs.emplace(ISOMaterialArea::MPF_ShuLiang, ToString<int>(typeInfo.typeCnt));
            else
                itr->second = ToString<int>(typeInfo.typeCnt);
        }

        // ����
        if (flags.hasFlag(ISOMaterialArea::MPF_DanZhong))
        {
            auto itr = attrs.find(ISOMaterialArea::MPF_DanZhong);
            if (itr == attrs.end())
            {
                char value[1024] = { 0 };
                auto pRefNode = pSpcoData->cmpRef().refNode();
                if (pRefNode != nullptr)
                {
                    bool bOk = false;
                    double doubleVar = 0.0;
                    auto pCmpd = pRefNode->getBDSub<WDBMCCmpwCMPD>();
                    if (pCmpd != nullptr)
                        doubleVar = FromString<double>(pCmpd->cweight, &bOk);
                    if (bOk)
                    {
                        typeInfo.weight = doubleVar;
                        // +0.005��Ϊ����������
                        sprintf_s(value, sizeof(value), "%.2f", typeInfo.weight + 0.005);
                    }
                }
                attrs.emplace(ISOMaterialArea::MPF_DanZhong, std::string(value));
            }
            else if (itr->second.empty())
            {
                auto pRefNode = pSpcoData->cmpRef().refNode();
                if (pRefNode != nullptr)
                {
                    bool bOk = false;
                    double doubleVar = 0.0;
                    auto pCmpd = pRefNode->getBDSub<WDBMCCmpwCMPD>();
                    if (pCmpd != nullptr)
                        doubleVar = FromString<double>(pCmpd->cweight, &bOk);
                    if (bOk)
                    {
                        typeInfo.weight = doubleVar;
                        // +0.005��Ϊ����������
                        char value[1024] = { 0 };
                        sprintf_s(value, sizeof(value), "%.2f", typeInfo.weight + 0.005);
                        itr->second = std::string(value);
                    }
                }
            }
        }

        // ����
        if (flags.hasFlag(ISOMaterialArea::MPF_ZongZhong))
        {
            char value[1024] = { 0 };
            typeInfo.allWeight = typeInfo.weight * typeInfo.typeCnt;
            if (typeInfo.allWeight > NumLimits<double>::Epsilon)
            {
                // +0.005��Ϊ����������
                sprintf_s(value, sizeof(value), "%.2f", typeInfo.allWeight + 0.005);
            }
            auto itr = attrs.find(ISOMaterialArea::MPF_ZongZhong);
            if (itr == attrs.end())
                attrs.emplace(ISOMaterialArea::MPF_ZongZhong, value);
            else
                itr->second = value;
        }
        return true;
    }
private:
    // ��ȡ��˨�ĳ���
    static std::string GetBoltLength(const WD::WDBMCSpecSPCO& node)
    {
        WDUnused(node);
        return "1";
    }
    // ��ȡSPCO�ڵ��µ�Btype
    static std::string GetSpcoNodeBType(const WDBMCSpecSPCO& spco)
    {
        auto pScomNode = spco.catRef().refNode();
        if (pScomNode == nullptr)
            return "";
        auto pScomData = pScomNode->getBDSub<WDBMCCataSCOM>();
        if (pScomData == nullptr)
            return "";
        auto& blRefArray = pScomData->blRefArray();
        if (blRefArray.empty())
            return "";
        for (auto& blRef : blRefArray)
        {
            auto blRefNode = blRef.refNode();
            if (blRefNode == nullptr)
                continue;
            auto blRefData = blRefNode->getBDSub<WDBMCCataBTSE>();
            if (blRefData == nullptr)
                continue;
            // ����ʹ��bltp�ڵ��btype
            for (auto& pChild : blRefNode->children())
            {
                if (pChild == nullptr)
                    continue;
                auto pBltpData = pChild->getBDSub<WDBMCCataBLTP>();
                if (pBltpData == nullptr)
                    continue;
                if (!pBltpData->bType().empty())
                    return pBltpData->bType();
            }
            if (!blRefData->bType().empty())
                return blRefData->bType();
        }
        return "";
    }
    // ��ȡSPCO�ڵ��µ�BDiamater
    static std::string GetSpcoNodeBDia(const WDBMCSpecSPCO& spco)
    {
        auto pScomNode = spco.catRef().refNode();
        if (pScomNode == nullptr)
            return "";
        auto pScomData = pScomNode->getBDSub<WDBMCCataSCOM>();
        if (pScomData == nullptr)
            return "";
        auto& blRefArray = pScomData->blRefArray();
        if (blRefArray.empty())
            return "";
        for (auto& blRef : blRefArray)
        {
            auto blRefNode = blRef.refNode();
            if (blRefNode == nullptr)
                continue;
            auto blRefData = blRefNode->getBDSub<WDBMCCataBTSE>();
            if (blRefData == nullptr)
                continue;
            // ����ʹ��bltp�ڵ��bDiameter
            for (auto& pChild : blRefNode->children())
            {
                if (pChild == nullptr)
                    continue;
                auto pBltpData = pChild->getBDSub<WDBMCCataBLTP>();
                if (pBltpData == nullptr)
                    continue;
                if (!pBltpData->bDiameter().empty())
                    return pBltpData->bDiameter();
            }
            if (!blRefData->bDiameter().empty())
                return blRefData->bDiameter();
        }
        return "";
    }
    // ���ݱ�����û�ȡspco�ڵ������
    static std::string GetNodeMiaoShu(const WD::WDBMCSpecSPCO& node, const ISOMaterialArea& materialTable)
    {
        std::string value;
        // ��ȡ��������
        if (materialTable.details != ISOMaterialArea::Details::DT_Null)
        {
            auto& detRef = node.detRef();
            auto pRefNode = detRef.refNode();
            if (pRefNode != nullptr)
            {
                auto pSmte = pRefNode->getBDSub<WDBMCCataSDTE>();
                if (pSmte != nullptr)
                {
                    switch (materialTable.details)
                    {
                    case ISOMaterialArea::Details::DT_RText:   value += pSmte->rText();    break;
                    case ISOMaterialArea::Details::DT_SText:   value += pSmte->sText();    break;
                    case ISOMaterialArea::Details::DT_TText:   value += pSmte->tText();    break;
                    default:break;
                    }
                }
            }
        }
        // ��ȡ��������
        if (materialTable.texture != ISOMaterialArea::Texture::TT_Null)
        {
            auto& matxtRef = node.matxtRef();
            auto pRefNode = matxtRef.refNode();
            if (pRefNode != nullptr)
            {
                auto pSmte = pRefNode->getBDSub<WDBMCCataSMTE>();
                if (pSmte != nullptr)
                {
                    if (!value.empty())
                        value.push_back(' ');
                    switch (materialTable.texture)
                    {
                    case ISOMaterialArea::Texture::TT_XText:   value += pSmte->xText();    break;
                    case ISOMaterialArea::Texture::TT_YText:   value += pSmte->yText();    break;
                    case ISOMaterialArea::Texture::TT_ZText:   value += pSmte->zText();    break;
                    default:break;
                    }
                }
            }
        }
        return value;
    }
    // ���ݽڵ����ͺ�spco�ڵ��ȡ���
    static std::string GetNodeGuiGe(const WD::WDBMCSpecSPCO& node, const char* nodeType)
    {
        if (nodeType == nullptr)
            return "";
        auto& catRef = node.catRef();
        if (!catRef.empty())
        {
            auto pRefNode = catRef.refNode();
            if (pRefNode != nullptr)
            {
                if (WDBMCCataComponent::Is(*pRefNode))
                {
                    // ��ͨ�Ĺ��Ϊ p1 x p3
                    if (WDBMDPipeTEE::IsType(nodeType))
                    {
                        const std::string p1 = WDBMCCataComponent::PtBore(*pRefNode, 0).value();
                        const std::string p3 = WDBMCCataComponent::PtBore(*pRefNode, 2).value();
                        if (!p1.empty() && !p3.empty())
                            return p1 + u8"��" + p3;
                        else if (!p1.empty())
                            return p1;
                        else if (!p3.empty())
                            return p3;
                    }
                    // ��Сͷ�Ĺ��Ϊ p1 x p2
                    else if (WDBMDPipeREDU::IsType(nodeType))
                    {
                        const std::string p1 = WDBMCCataComponent::PtBore(*pRefNode, 0).value();
                        const std::string p2 = WDBMCCataComponent::PtBore(*pRefNode, 1).value();
                        if (!p1.empty() && !p2.empty())
                            return p1 + u8"��" + p2;
                        else if (!p1.empty())
                            return p1;
                        else if (!p2.empty())
                            return p2;
                    }
                    // �����ܼ����� p1
                    else
                        return WDBMCCataComponent::PtBore(*pRefNode, 0).value();
                }
            }
        }
        return "";
    }
    // ��ȡspco�ڵ�����ϴ���
    static std::string GetNodeWuLiaoDaiMa(const WD::WDNode& node)
    {
        std::string value = node.name();
        auto index = value.find('/');
        while(index != std::string::npos)
        {
            value = std::string(value.begin() + index + 1, value.end());
            index = value.find('/');
        }

        std::regex partten("[:@.\\+\\-&]");
        std::sregex_iterator itrRegex(value.begin(), value.end(), partten);
        if (itrRegex != std::sregex_iterator())
            value = itrRegex->prefix();

        return value;
    }
private:
    WD::WDNode::SharedPtr _pPreNode = nullptr;
};

bool ISOPipingGenerate::generate(ISOPolttingArea& paper
    , ISOMaterialArea& materialTable
    , WDNode& branchNode
    , bool drawToPolttingArea)
{
    auto pBRANData = WDBMDPipeBRAN::Data(branchNode);
    if (pBRANData == nullptr)
    {
        assert(false);
        return false;
    }
    TuLine line;
    DKeyPoint hAPt;
    hAPt.position   = WDBMDPipeBRAN::HPos(branchNode, WDWRTType::WRT_World);
    hAPt.direction  = - WDBMDPipeBRAN::HDir(branchNode, WDWRTType::WRT_World);
    line.push<TuPosNode>(TuPosNode::T_BranHPos, hAPt, pBRANData->hRef());
    // �Ƿ���Ҫ��ʶ�����ͷ
    bool bFlowArrow = false;
    // �Ƿ���Ҫ��ʶͨ��
    bool bBore      = false;
    for (size_t i = 0; i < branchNode.children().size(); ++i)
    {
        auto pNode = branchNode.children()[i];
        if (pNode == nullptr)
            continue;
        // ���ŵ�, ����
        if (WDBMDPipeATTA::Is(*pNode))
        {
            continue;
        }
        // ����, ����
        if (WDBMDPipeWELD::Is(*pNode))
        {
            continue;
        }
        // ֱ��
        else if (WDBMDPipeTUBI::Is(*pNode))
        {
            TuTubiNode* pRNode  = line.push<TuTubiNode>(pNode);
            pRNode->bFlowArrow  = bFlowArrow;
            bFlowArrow          = false;
            pRNode->bBore       = bBore;
            bBore               = false;
        }
        // �ܼ�
        else if (WDBMDPipeCOMS::Is(*pNode))
        {
            auto pComData = WDBMDPipeCOMS::Data(*pNode);
            assert(pComData != nullptr);

            line.push<TuComNode>(pNode);

            // TEE ���� OLETԪ����ĵ�һ��TUBI�ϣ���Ҫ���������ͷ
            if (pComData->isAnyOfType(WDBMDPipeTEE::Type, WDBMDPipeOLET::Type))
                bFlowArrow = true;
            // ����ܼ��и���ͨ��(����ֱ��)������һ��ֱ����Ҫ��ʶͨ��
            auto pPtA = pComData->arrivePoint();
            auto pPbL = pComData->leavePoint();
            if (pPtA != nullptr && pPbL != nullptr && pPtA->bore() != pPbL->bore())
                bBore = true;
        }
    }

    DKeyPoint tAPt;
    tAPt.position    = WDBMDPipeBRAN::TPos(branchNode, WDWRTType::WRT_World);
    tAPt.direction   = WDBMDPipeBRAN::TDir(branchNode, WDWRTType::WRT_World);
    line.push<TuPosNode>(TuPosNode::T_BranTPos, tAPt, pBRANData->tRef());
    // ��ȡ�����е�ֱ�ܣ�ָ����һ��ֱ����Ҫ ��ʶͨ���������ͷ�����һ��ֱ����Ҫ��ʶͨ��
    std::vector<TuTubiNode*> tubiNodes;
    tubiNodes.reserve(line.nodes().size());
    for (auto pNode : line.nodes())
    {
        if (pNode == nullptr)
            continue;
        if (pNode->isTubiNode())
            tubiNodes.push_back(static_cast<TuTubiNode*>(pNode));
    }
    if (!tubiNodes.empty())
    {
        tubiNodes.front()->bBore = true;
        tubiNodes.front()->bFlowArrow = true;
        tubiNodes.back()->bBore = true;
    }
    // ������ͺ������λ�ü��㵽��Ӧ��ֱ�ܽڵ���
    for (auto pNode : branchNode.children())
    {
        if (pNode == nullptr)
            continue;
        if (WDBMDPipeATTA::Is(*pNode))
        {
            line.addATTANode(pNode);
        }
        else if (WDBMDPipeWELD::Is(*pNode))
        {
            line.addWELDNode(pNode);
        }
    }

    // ���ɹܼ�����˨����ĸ����Ȧ�ȵı��
    line.genNumber();

    // ���Ƶ���ͼ��
    if (drawToPolttingArea && paper.pGroup != nullptr)
    {
        ISOSVGPainter2D painter(paper.pGroup);
        line.draw(paper, painter);
    }

    // ͳ�Ʋ���
    MaterialDataStatistics data(materialTable);
    double tubeLength = 0.0;
    for (auto& each : line.nodes())
    {
        if (each == nullptr)
            continue;
        if (each->number <= 0)
            continue;
        if (each->isComNode())
        {
            auto pComNode = dynamic_cast<TuComNode*>(each);
            if (pComNode != nullptr)
            {
                if (!data.execComNode(*pComNode))
                    assert(false);
            }
        }
        else if (each->isTubiNode())
        {
            auto pTubiNode = dynamic_cast<TuTubiNode*>(each);
            if (pTubiNode != nullptr)
            {
                if (!data.execTubiNode(*pTubiNode, tubeLength))
                    assert(false);
            }
        }
    }

    return updateMaterialTable(paper, materialTable, data);
}
bool ISOPipingGenerate::updateMaterialTable(ISOPolttingArea& paper, ISOMaterialArea& materialTable, MaterialDataStatistics& data)
{
    auto& erectionTable = materialTable.erectionTable;
    auto& fabricationTable = materialTable.fabricationTable;
    DVec2 position = materialTable.position * paper.uCvt.paperToPixelRatio();

    ISOTableWidget::TableRemkras notes;
    notes.height = paper.uCvt.paperToPixel(6.0);
    notes.bVisible = materialTable.flags.hasFlag(ISOMaterialArea::MPF_DanZhong) || materialTable.flags.hasFlag(ISOMaterialArea::MPF_ZongZhong);
    notes.textFont.pixelSize = paper.uCvt.paperToPixel(3.0);
    notes.horizontalOffset = paper.uCvt.paperToPixel(5.0);
    notes.textAlign = WDDIMAlign::HA_Right;

    ISOSVGPainter2D painter(paper.pGroup);
    double totalWeight = 0.0;
    if (!data.typeFabricationMap.empty())
    {
        erectionTable.bVisible = true;
        // index��1��ʼ����Ϊ0�Ǳ�ͷ(Ŀǰ��ͷ��δ��������)
        int row = 1;
        fabricationTable.setRowCnt(data.typeFabricationMap.size() + row);
        for (auto itr = data.typeFabricationMap.begin(); itr != data.typeFabricationMap.end(); ++itr, row++)
        {
            auto& attrs = itr->second.nodeAttrs;
            int col = 0;
            for (auto attrItr = attrs.begin(); attrItr != attrs.end(); ++attrItr, col++)
                fabricationTable.setText(row, col, attrItr->second);
            totalWeight += itr->second.allWeight;
        }
    }
    else
    {
        fabricationTable.bVisible = false;
    }
    if (!data.typeErectionMap.empty())
    {
        erectionTable.bVisible = true;
        fabricationTable.notes.bVisible = false;
        // index��1��ʼ����Ϊ0�Ǳ�ͷ(Ŀǰ��ͷ��δ��������)
        int row = 1;
        erectionTable.setRowCnt(data.typeErectionMap.size() + row);
        for (auto itr = data.typeErectionMap.begin(); itr != data.typeErectionMap.end(); ++itr, row++)
        {
            auto& attrs = itr->second.nodeAttrs;
            int col = 0;
            for (auto attrItr = attrs.begin(); attrItr != attrs.end(); ++attrItr, col++)
                erectionTable.setText(row, col, attrItr->second);
            totalWeight += itr->second.allWeight;
        }
        char value[1024] = { 0 };
        // +0.005��Ϊ����������
        sprintf_s(value, sizeof(value), u8"����:%.2f", totalWeight + 0.005);
        notes.text = value;
        if (totalWeight <= NumLimits<double>::Epsilon)
            notes.bVisible = false;
        erectionTable.notes = notes;
    }
    else
    {
        char value[1024] = { 0 };
        // +0.005��Ϊ����������
        sprintf_s(value, sizeof(value), u8"����:%.2f", totalWeight + 0.005);
        notes.text = value;
        if (totalWeight <= NumLimits<double>::Epsilon)
            notes.bVisible = false;
        fabricationTable.notes = notes;
        erectionTable.bVisible = false;
    }
    double height = 0.0;
    erectionTable.setColAlign(WDDIMAlign{WDDIMAlign::HA_Left, WDDIMAlign::VA_Center}, 1);
    erectionTable.setColAlign(WDDIMAlign{WDDIMAlign::HA_Left, WDDIMAlign::VA_Center}, 3);
    erectionTable.setColAlign(WDDIMAlign{WDDIMAlign::HA_Left, WDDIMAlign::VA_Center}, 4);
    fabricationTable.setColAlign(WDDIMAlign{WDDIMAlign::HA_Left, WDDIMAlign::VA_Center}, 1);
    fabricationTable.setColAlign(WDDIMAlign{WDDIMAlign::HA_Left, WDDIMAlign::VA_Center}, 3);
    fabricationTable.setColAlign(WDDIMAlign{WDDIMAlign::HA_Left, WDDIMAlign::VA_Center}, 4);
    ISOSVGPainter2D::ISOFont font;
    font.family = u8"����";
    font.pixelSize = paper.uCvt.paperToPixel(3);
    fabricationTable.position = position;
    fabricationTable.update(painter, font, height);
    erectionTable.position = position + DVec2(0, height);
    erectionTable.update(painter, font);
    return true;
}
WD_NAMESPACE_END

