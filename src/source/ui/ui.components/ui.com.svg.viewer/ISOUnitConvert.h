#pragma once
#include "core/cameras/WDOrthographicCamera.h"
#include "core/math/DirectionParser.h"

WD_NAMESPACE_BEGIN

/**
 * @brief 单位转换、坐标转换
 * 这是纸张与屏幕像素的一个转换算法，需要指定分辨率(DPI: 表示1英寸有多少像素点, 即: 150DPI 表示150像素/英寸)
 * 其中 :
 *  1英寸 = 25.4毫米
 *  因此 pixel = length(mm) * 1.0 / 25.4 * DPI
*/
class ISOUnitConvert
{
public:
    enum UnitType
    {
        UT_Pixel,
        UT_mm,
    };
public:
    // 毫米转换到英寸
    static constexpr double MMToInch = (1.0 / 25.4);
public:
    ISOUnitConvert() 
    {
        _pCamera = WDOrthographicCamera::MakeShared();
    }
public:
    /**
     * @brief 设置分辨率, 用于图纸单位与屏幕像素的互相转换
    */
    inline void setDPI(double dpi);
    /**
     * @brief 更新相机, 用于屏幕像素与世界坐标单位的互相转换
     * @param viewSize 视口宽高
     * @param aabb 世界坐标包围盒，图纸中出现的所有节点的包围盒
     * @param isoDir 视角方向
     * @return 是否更新成功
    */
    template <typename T1, typename T2>
    bool updateCamera(const TVec2<T1>& viewSize, const TAabb3<T2>& aabb, const std::string& isoDir = "X 45 Y 35 -Z");
public:
    /**
     * @brief 获取分辨率
    */
    inline double dpi() const;
    /**
     * @brief 获取相机
    */
    inline const WDOrthographicCamera& camera() const;
    /**
     * @brief 设置相机缩放
    */
    inline void scaleCameraZoom(double scaler)
    {
        _pCamera->setZoom(scaler * _pCamera->zoom());
        _pCamera->update();
    }
public:
    /**
     * @brief 获取 图纸的单位的值(毫米) 转换到 屏幕像素单位的值(像素) 的比
     * @tparam T 数值类型
     * @return 比率
    */
    inline double paperToPixelRatio() const;
    /**
     * @brief 图纸的单位的值(毫米) 转换到 屏幕像素单位的值(像素)
     * @tparam T 数值类型
     * @param paper 图纸单位的值
     * @return 屏幕像素值
    */
    template <typename T>
    inline T paperToPixel(const T& paper) const;
    /**
     * @brief 屏幕像素单位的值(像素) 转换到 图纸的单位的值(毫米)
     * @tparam T 数值类型
     * @param pixel 屏幕像素值
     * @return 图纸单位的值
    */
    template <typename T>
    inline T pixelToPaper(const T& pixel) const;
    /**
     * @brief 获取 屏幕像素单位的值(像素) 到 世界坐标单位的值(世界坐标的单位也是毫米) 的比
     * @tparam T 数值类型
     * @return 比率
    */
    inline double pixelToWorldRatio() const;
    /**
     * @brief 屏幕像素单位的值(像素) 转换到 世界坐标单位的值(世界坐标的单位也是毫米)
     * @tparam T 数值类型
     * @param pixel 屏幕像素值
     * @return 世界坐标单位的值
    */
    template <typename T>
    inline T pixelToWorld(const T& pixel) const;
    /**
     * @brief 世界坐标单位的值(世界坐标的单位也是毫米) 转换到 屏幕像素单位的值(像素)
     * @tparam T 数值类型
     * @param world 世界坐标单位的值
     * @return 屏幕像素值
    */
    template <typename T>
    inline T worldToPixel(const T& world) const;
    /**
     * @brief 获取 图纸单位的值(毫米) 到 世界坐标单位的值(世界坐标的单位也是毫米) 的比
     * @tparam T 数值类型
     * @return 比率
    */
    inline double paperToWorldRatio() const;
    /**
     * @brief 图纸单位的值(毫米) 转换到 世界坐标单位的值(世界坐标的单位也是毫米)
     * @tparam T 数值类型
     * @param paper 图纸单位的值
     * @return 世界坐标单位的值
    */
    template <typename T>
    inline T paperToWorld(const T& paper) const;
    /**
     * @brief 世界坐标单位的值(世界坐标的单位也是毫米) 转换到 图纸的单位的值(毫米)
     * @tparam T 数值类型
     * @param paper 世界坐标单位的值
     * @return 图纸的单位的值
    */
    template <typename T>
    inline T worldToPaper(const T& world) const;
    /**
     * @brief 世界坐标转换到屏幕坐标
     * @tparam T 数值类型
     * @param world 世界坐标
     * @param bInvertY 是否翻转转换后的屏幕坐标的Y值
     *      原因是相机转换后的屏幕坐标原点为左下角，而大多窗口系统的屏幕坐标原点为坐上角，因此需要翻转Y
     * @return 屏幕坐标
    */
    inline DVec2 worldToScreen(const DVec3& world, bool bInvertY = true) const;
private:
    // 分辨率(DPI: 表示1英寸有多少像素点, 即: 150DPI 表示150像素/英寸)
    double _dpi = 300.0;
    // 正交投影相机, 转换绘图区的坐标
    WDOrthographicCamera::SharedPtr _pCamera;
};
using ISOUnitType = ISOUnitConvert::UnitType;

WD_NAMESPACE_END

#include "ISOUnitConvert.inl"

