#include "Collaboration.h"

#include <gflags/gflags.h>

#include "WDRapidxml.h"
#include "core/businessModule/design/WDBMDesign.h"
#include "core/businessModule/catalog/WDBMCatalog.h"
#include "core/businessModule/WDBMAuthorityMgr.h"
#include "core/common/WDStringConvert.h"
#include "core/WDLicense.h"
#include "core/WDBlockingTask.h"
#include "core/message/WDMessage.h"

#include "common/DesignGlog.h"
#include "service/DesignKafkaService.h"
#include "rpc/client/DesignServiceConnectionPool.h"
#include "rpc/client/DesignServiceClient.h"
#include "service/ConfigParser.h"
#include "rpc/minio/MinioTaskState.h"
#include "rpc/minio/MinioDownloader.h"
#include "store/db_store/DbStore.h"
#include "service/DesignTLVFile.h"
#include "service/DesignQueue2RpcService.h"
#include "serialize/WDNodeCache.h"

static constexpr const char* XmlFileName = "config-DC.xml";

static constexpr const char* Cfg_Key_Active = "Cfg_Key_Active";
static constexpr const char* Cfg_Key_Db = "Cfg_Key_Db";
static constexpr const char* Cfg_Key_Grpc = "Cfg_Key_Grpc";
static constexpr const char* Cfg_Key_Immediately_Pull = "Cfg_Key_Immediately_Pull";
static constexpr const char* Cfg_Key_Auto_Unlock = "Cfg_Key_Auto_Unlock";

static constexpr const char* Classification_Design = "design";
static constexpr const char* Classification_Catalog = "catalog";
static constexpr const int32_t CheckIn_Flag = 1;
static constexpr const int32_t CheckOut_Flag = 0;
static constexpr const int32_t Self_Flag = 0;
static constexpr const int32_t Descendants_Flag = 1;
static constexpr const char* CatalogManager = "catelogManager";

enum CBPermission
{
    // 无权限
    CBP_None = 0,
    // 可读
    CBP_Read = 1,
    // 读写
    CBP_ReadWrite = 2,
};
static CBPermission ToCBPermission(uint8_t value)
{
    if (value == 0 || value == 4)
        return CBP_None;
    else if (value == 1)
        return CBP_Read;
    else if (value == 3)
        return CBP_ReadWrite;

    return CBP_None;
}
static const CBPermission Root_Permission = CBP_Read;

/**
* @brief 协同权限标志
*/
//enum PMFlag
//{
//    // 无
//    PMF_None        =   0,
//    // 可读
//    PMF_Read        =   (1 << 0),
//    // 可写
//    PMF_Write       =   (1 << 1),
//};
//using PMFlags = WD::WDFlags<PMFlag, unsigned>;
//static PMFlags ToPMFlags(uint8_t value)
//{
//    if (value > 3)
//        return PMFlags();
//    else
//        return PMFlags(value);
//}


namespace NSNodeTreeRecord
{
    /**
     * @brief 父节点rid
     * @param record record对象
     */
    static int64_t Parent(const design::NodeTreeRecord& record)
    {
        return record.id();
    }

    /**
    * @brief 获取子节点列表中的子节点rid列表
    * @param vec 子节点列表
    */
    static std::vector<int64_t> Children(const design::NodeTreeRecord& record)
    {
        std::vector<int64_t> children;
        const auto& vec = record.children();

        for (size_t i = 0; i < vec.vec_int64_size(); ++i)
        {
            children.push_back(vec.vec_int64((int)i));
        }
        return children;
    }

    static void Log(const design::NodeTreeRecord& record)
    {
        std::string str = "TreeAction : ";
        str += std::to_string(record.id());
        str += "(";
        const auto& vec = record.children();
        for (int i = 0; i < vec.vec_int64_size(); ++i)
        {
            str += std::to_string(vec.vec_int64(i));
            if (i != vec.vec_int64_size() - 1)
                str += ", ";
        }
        str += ")";
        LOG(INFO) << str;
    }
};

/**
 * @brief 根据节点所属模块获取队列节点类型
 * @param node 节点
 * @return 队列节点类型
 */
static wiz::DesignNode2Queue::NodeType NodeBMType(WD::WDNode& node)
{
    if (node.getBMSub<WD::WDBMDesign>() != nullptr)
        return wiz::DesignNode2Queue::NodeType::DESIGN;
    else if (node.getBMSub<WD::WDBMCatalog>() != nullptr)
        return wiz::DesignNode2Queue::NodeType::CATALOG;

    assert(false);
    // 理论上不存在这种情况，出现视为DESIGN
    return wiz::DesignNode2Queue::NodeType::DESIGN;
}

/**
 * @brief 获取项目信息数据
 * @param collaboration 协同接口
 * @return 项目信息数据
 */
static design::ProjectInfo DesignProjectInfo(Collaboration& collaboration)
{
    design::UserInfo user;
    user.set_user(collaboration.userInfo().id);
    user.set_pwd(collaboration.userInfo().password);
    design::ProjectInfo projectInfo;
    projectInfo.set_user(user.user());
    projectInfo.set_projectcode(collaboration.projectInfo().code);
    return projectInfo;
}

Collaboration::Collaboration(WD::WDCore& core)
    : _core(core)
{
    _pLoginServer = new LoginServer(core, *this);
    _pModelServer = new ModelServer(core, *this);

    // 默认不激活协同服务
    _isCollaboration = false;

    // 读取DC配置
    this->readConfig();

    // 初始化
    this->init();
}

Collaboration::~Collaboration()
{
    if (_pLoginServer != nullptr)
    {
        delete _pLoginServer;
        _pLoginServer = nullptr;
    }
    if (_pModelServer != nullptr)
    {
        delete _pModelServer;
        _pModelServer = nullptr;
    }
}

void Collaboration::init()
{
    // 初始化日志
    auto logPath = std::string(_core.exeDirPath()) + "/logs";
    wiz::initGoogleLogging("Collaboration", logPath, true, 7);

    // 初始化grpc服务
    auto& pool = wiz::DesignServiceConnectionPool::getInstance();
    pool.initialize(_cfgInfo.grpc);
}

static void ParseXMLDoc(WD::XMLDoc& doc, char* data)
{
    try
    {
        doc.parse<0>(data);
    }
    catch (const rapidxml::parse_error&)
    {
        assert(false);
    }
    catch (...)
    {
        assert(false);
    }
}

void Collaboration::readConfig()
{
    std::string filePath = std::string(_core.dataDirPath()) + std::string(XmlFileName);
    WD::WDFileReader file(filePath);
    if (file.isBad())
        return;
    file.readAll();
    if (file.length() == 0)
        return;

    WD::XMLDoc doc;
    ParseXMLDoc(doc, (char*)file.data());
    WD::XMLNode* pXmlNodeRoot = doc.first_node("Root");
    if (pXmlNodeRoot == nullptr)
        return;
    for (WD::XMLNode* pXmlNode = pXmlNodeRoot->first_node("item")
         ; pXmlNode
         ; pXmlNode = pXmlNode->next_sibling())
    {
        auto pKeyAttr = pXmlNode->first_attribute("key");
        if (pKeyAttr == nullptr)
            continue;
        std::string key = pKeyAttr->value();

        std::string value;
        auto pValueAttr = pXmlNode->first_attribute("value");
        if (pValueAttr != nullptr)
        {
            value = pValueAttr->value();
        }

        if (key == Cfg_Key_Active)
        {
            bool bOk = false;
            auto realValue = WD::FromString<bool>(value, &bOk);
            if (bOk)
            {
                _isCollaboration = realValue;
            }
        }
        else if (key == Cfg_Key_Db)
        {
            _cfgInfo.db = value;
        }
        else if (key == Cfg_Key_Grpc)
        {
            _cfgInfo.grpc = value;
        }
        else if (key == Cfg_Key_Immediately_Pull)
        {
            bool bOk = false;
            auto realValue = WD::FromString<bool>(value, &bOk);
            if (bOk)
            {
                _cfgInfo.immediately_pull = realValue;
            }
        }
        else if (key == Cfg_Key_Auto_Unlock)
        {
            bool bOk = false;
            auto realValue = WD::FromString<bool>(value, &bOk);
            if (bOk)
            {
                _cfgInfo.auto_unlock = realValue;
            }
        }
    }
}


LoginServer::LoginServer(WD::WDCore& core, Collaboration& collaboration)
    : _core(core)
      , _collaboration(collaboration)
      , _nodeCache(WD::WDNodeCacheByType::getInstance())
{
}

LoginServer::~LoginServer()
{
}

LoginServer::LoginUserConfig LoginServer::login(std::string_view userName, std::string_view password) const
{
    LoginUserConfig userConfig;
    // 客户端服务
    auto& service = wiz::DesignServiceClient::getInstance();

    design::UserInfo user;
    user.set_user(userName);
    user.set_pwd(password);

    // 先获取公钥
    auto userConfigStr = service.LoginChain(user);
    if (!userConfigStr)
    {
        LOG(INFO) << "登录失败";
        return userConfig;
    }
    LOG(INFO) << userConfigStr.value();

    // 登录成功缓存用户账号密码
    _collaboration.userInfo().id = userName;
    _collaboration.userInfo().password = password;

    // 解析登录用户配置
    userConfig = this->parseUserConfig(userConfigStr.value());

    return userConfig;
}

// 下载minio文件并导入本地store
void LoginServer::downloadFromMinioAndUpsetDB(wiz::ServiceConfig serviceConfig
                                            , WD::store::IStore& store
                                            , std::string_view projCode) const
{
    auto progressCallback = [](const wiz::TaskProgress& progress)
    {
        static int i = 0;

        LOG_IF(
            INFO, i%10==0
            || progress.state == wiz::MinioTaskState::COMPLETED
            || progress.state == wiz::MinioTaskState::FAILED
        ) << "\rDownloading " << progress.objectName
                    << ": " << std::fixed << std::setprecision(2) << progress.getPercentage() << "% "
                    << "(" << progress.bytesDownloaded << "/" << progress.totalBytes << " bytes) "
                    << std::setprecision(2) << (progress.speed / 1024.0 / 1024.0) << " MB/s";
        ++i;
    };

    std::vector<std::tuple<std::string, std::string, std::string>> tasks;
    auto projDir =  std::string(_core.dataDirPath()) + "db/projects/" + projCode.data() + "/";
    for (const auto& sn : serviceConfig.minio->nodeSnapshotBasePath)
    {
        tasks.push_back(std::make_tuple(
            serviceConfig.minio->bucketName,
            sn.fileName,
            projDir + sn.fileName
        ));
    }
    wiz::MinioDownloader downloader(
        serviceConfig.minio->endpoint,
        serviceConfig.minio->accessKey,
        serviceConfig.minio->secretKey,
        false,
        4
    );
    auto taskIds = downloader.addTasks(tasks, progressCallback);
    downloader.waitForCompletion(5000);
    // 导入本地
    for (const auto& t : tasks)
    {
        auto tvf = wiz::DesignTLVFile(std::get<2>(t));
        tvf.updateStore(store);
    }
}

// 生成硬件ID哈希
static std::string GenerateHardwareHash()
{
    return WD::WDLicense::DataMd5().toString();
}

bool LoginServer::openProject(const UserCfgProj& project, std::string_view userRole)
{
    WDUnused(userRole);

    // 启动项目成功则缓存项目信息
    _collaboration.projectInfo().code = project.code;
    _collaboration.projectInfo().name = project.name;
    _collaboration.projectInfo().id = project.id;

    auto& service = wiz::DesignServiceClient::getInstance();
    // 设置项目信息
    design::UserInfo user;
    user.set_user(_collaboration.userInfo().id);
    user.set_pwd(_collaboration.userInfo().password);
    // TODO: SERVER (目前没有关联角色)
    // user.set_role(_collaboration.userInfo().roleCode);
    design::ProjectInfo projectInfo;
    projectInfo.set_user(user.user());
    projectInfo.set_projectcode(project.code);

    // 获取服务配置信息
    auto configJson = service.getConfig(projectInfo);
    // LOG(INFO) << "Raw config from service: " << configJson;
    auto serviceConfig = wiz::ConfigParser::parseFromJson(configJson);
    auto mv = serviceConfig.minio->getLatestSnapshotTime();
    LOG(INFO) << "server config:" << serviceConfig.toJson() << "|last snapshot time:" << mv.first << " - " << mv.second
        << " \ndb path:" << this->projDbPath();
    auto fileMaxUpdateTime = mv.second;
    auto dbPrefix = std::to_string(serviceConfig.projectInfo->id);
    auto commonStore = std::make_shared<WD::store::CommonStore>(this->projDbPath());

    auto storeUPtr = std::make_unique<WD::store::DbStore>(this->projDbPath(), dbPrefix, 10); // 10个表，这里的参数不要改动
    // 将store注入queue，这样消费数据及时保存
    auto& queue = wiz::DesignNode2Queue::getInstance();
    // 初始化节点队列
    queue.initialize(projectInfo, commonStore, &_core.getBMDesign(), &_core.getBMCatalog(), 0);
    queue.setStore(std::move(storeUPtr));
    auto& store = queue.getStore();
    // 初始化节点缓存
    _nodeCache.initialize(&store, &queue.getSerializer(design::NodeType::DESIGN),
                          &queue.getSerializer(design::NodeType::CATALOG));
    // 监测队列，发往服务端
    auto& rpcService = wiz::DesignQueue2RpcService::getInstance();
    rpcService.initialize(projectInfo);
    rpcService.start();
    rpcService.self().detach();

    auto& kafkaService = wiz::DesignKafkaService::getInstance();
    const auto& kafkaConfig = serviceConfig.kafka.value();
    // todo 这里的group id 需要保持唯一
    auto hardwareHash = GenerateHardwareHash();
    const std::string groupId = "dc-" + projectInfo.projectcode() + user.user() + hardwareHash;
    kafkaService.initialize(kafkaConfig.bootstrapServers, kafkaConfig.topic, groupId, 0);

    // 对比本地时间与远程时间； // 模型类型可以只填DESIGN； CATALOG因长期不变 导致差异过大
    auto localMaxUpdateTime = store.fetchMaxUpdateTime(WD::store::NodeType::DESIGN);
    if (serviceConfig.minio.value().nodeSnapshotBasePath.empty())
    {
        // 没有文件，项目未实现初始化；直接消费kafka; 等待导入
        kafkaService.start();
    }
    else
    {
        if (fileMaxUpdateTime - localMaxUpdateTime > 2 * 24 * 60 * 60 * 1000)
        {
            LOG(INFO) << "begin downloading file:" << fileMaxUpdateTime << " - " << localMaxUpdateTime;
            // 时间差异过大，直接下载;

            // todo 对比 offset 差异是否过大
            downloadFromMinioAndUpsetDB(serviceConfig, store, project.code);
            localMaxUpdateTime = fileMaxUpdateTime; // 更新完之后时间就与远程文件一致
            // localMaxUpdateTime 可能不变，导致拉取; fileMaxUpdateTime 是镜像的生成时间
            // 构建一个虚拟节点0,将其中的时间设置为最后的时间fileMaxUpdateTime
            design::NodeAttrsRecord root;
            root.set_id(0);
            auto info = root.mutable_additionalinfo();
            info->set_time(fileMaxUpdateTime);
            store.upsetNodeAttrsRecord(root, "00000000-0000-0000-0000-000000000000", design::NodeType::DESIGN,
                                       WD::store::IStore::FromServer);
        }
        LOG(INFO) << "will start consumer from time:" << localMaxUpdateTime;
        auto partitionOffset = kafkaService.
            getOffsetByTimestamp(localMaxUpdateTime, {0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11});
        // logging 用于定位问题
        for (auto& [topic, partition, offset] : partitionOffset)
        {
            LOG(INFO) << "start consumer from " << topic << " " << partition << " " << offset;
        }
        kafkaService.startFromTopicPartitions(partitionOffset);
    }

    return true;
}

static void RecursionCacheNode(WD::WDBMBase& mgr, WD::WDNode::SharedPtr pParent, WD::WDNodeCache& cache, const std::map<uint64_t, CBPermission>& permissions)
{
    static std::set<int64_t> visited;
    if (pParent == nullptr)
        return;
    auto parRid = pParent->getRemoteId();
    const auto tChildrenRids = cache.getChildrenIdsFromStoreNoLock(parRid);
    if (tChildrenRids.empty())
        return;
    // 没有可读权限的节点直接不进行序列化
    std::vector<int64_t> childrenRids;
    for (const auto& tChildRid : tChildrenRids)
    {
        auto pItr = permissions.find(tChildRid);
        if (pItr == permissions.end())
        {
            // 没有权限说明时继承父节点，逻辑到此处代表父节点一定有读权限，此时不做处理准备加入后续的序列化
        }
        else
        {
            // 无权限则直接不进行后续的序列化
            if (pItr->second == CBPermission::CBP_None)
                continue;
        }

        childrenRids.push_back(tChildRid);
    }
    const auto children = cache.getNodes(childrenRids);
    // if get
    for (const auto& pChild : children)
    {
        if (pChild == nullptr)
            continue;
        auto cid = pChild->getRemoteId();
        // 判断有无写入权限
        auto pItr = permissions.find(cid);
        if (pItr == permissions.end())
        {
            // 没有权限说明时继承父节点
            if (pParent->flags().hasFlag(WD::WDNode::F_Editable))
            {
                auto flags = pChild->flags();
                pChild->setFlags(flags.addFlag(WD::WDNode::F_Editable));
            }
        }
        else
        {
            // 无权限则直接不进行后续的序列化
            if (pItr->second == CBPermission::CBP_ReadWrite)
            {
                auto flags = pChild->flags();
                pChild->setFlags(flags.addFlag(WD::WDNode::F_Editable));
            }
        }
        if (visited.find(cid) != visited.end())
        {
            LOG(WARNING) << "visited: " << cid << ", pid:" << parRid
                << ", children size: " << childrenRids.size() << "|" << children.size();
            continue;
        }
        visited.insert(cid);
        // 此时先不更新，后续统一更新
        mgr.setParent(pChild, pParent, false);
        RecursionCacheNode(mgr, pChild, cache, permissions);
    }
}

void LoginServer::load() const
{
    auto& cataMgr = _core.getBMCatalog();
    auto pCataRoot = cataMgr.root();
    auto& desiMgr = _core.getBMDesign();
    auto pDesiRoot = desiMgr.root();
    if (pCataRoot == nullptr || pDesiRoot == nullptr)
        return ;

    // 先清除根节点的所有子节点
    cataMgr.destroy(pCataRoot->children(), false);
    desiMgr.destroy(pDesiRoot->children(), false);

    // 获取节点权限
    auto& service = wiz::DesignServiceClient::getInstance();
    const auto& tPermissions = service.getPermissions(DesignProjectInfo(_collaboration));
    std::map<uint64_t, CBPermission> permissions;
    for (const auto& [rid, flag] : tPermissions)
    {
        permissions[rid] = ToCBPermission(flag);
    }

    // 本地数据库中的数据转化为节点预挂载在树上
    RecursionCacheNode(_core.getBMDesign(), pDesiRoot, _nodeCache.getCache(design::NodeType::DESIGN), permissions);
    RecursionCacheNode(_core.getBMCatalog(), pCataRoot, _nodeCache.getCache(design::NodeType::CATALOG), permissions);

    // 收集引用，懒加载考虑调整此处
    cataMgr.updateRefs({ pCataRoot });
    pCataRoot->update(true);
    cataMgr.updateRefs({ pDesiRoot });
    pDesiRoot->update(true);

    // 缓存节点数据
    _collaboration.modelServer().cacheNodeData();
}

LoginServer::LoginUserConfig LoginServer::parseUserConfig(std::string_view str) const
{
    LoginUserConfig userConfig;

    WD::JsonDoc doc;
    doc.Parse((char*)str.data(), str.size());
    if (doc.HasParseError())
        return userConfig;
    if (!doc.IsObject())
        return userConfig;

    // TODO: SERVER 目前的数据结构不是最终形态

    // 获取角色列表
    std::vector<UserCfgRole> userRoles;
    WD::JsonValue objRoles = GetJsonValueSafely<WD::JsonValue>(doc, "role");
    if (objRoles.IsArray() && !objRoles.Empty())
    {
        for (auto& objRole : objRoles.GetArray())
        {
            auto& backEle = userRoles.emplace_back();
            backEle.name = GetJsonValueSafely<std::string>(objRole, "name");
            backEle.code = GetJsonValueSafely<std::string>(objRole, "code");
        }
    }
    // 获取项目列表
    WD::JsonValue objProjs = GetJsonValueSafely<WD::JsonValue>(doc, "projects");
    if (objProjs.IsArray() && !objProjs.Empty())
    {
        for (auto& objProj : objProjs.GetArray())
        {
            auto& backEle = userConfig.emplace_back();
            backEle.id = GetJsonValueSafely<uint64_t>(objProj, "id");
            backEle.name = GetJsonValueSafely<std::string>(objProj, "name");
            backEle.code = GetJsonValueSafely<std::string>(objProj, "code");
            backEle.roles = userRoles;
        }
    }

    // 获取元件库权限
    WD::JsonValue objMenuResources = GetJsonValueSafely<WD::JsonValue>(doc, "menuResources");
    if (objMenuResources.IsObject())
    {
        auto objBtns = GetJsonValueSafely<WD::JsonValue>(objMenuResources, "button");
        if (objBtns.IsArray() && !objBtns.Empty())
        {
            for (auto& objBtn : objBtns.GetArray())
            {
                auto codeValue = GetJsonValueSafely<std::string>(objBtn, "code");
                if (codeValue == CatalogManager)
                {
                    _collaboration.userInfo().catalogPerm = true;
                    break;
                }
            }
        }
    }

    return userConfig;
}

std::string LoginServer::projDbPath()
{
    auto ret = _core.dataDirPath() + _collaboration.cfgInfo().db + _collaboration.projectInfo().code;
    return ret;
}


/**
 * @brief 将节点保存到本地db
 * @param queue 节点队列
 * @param pNodes 节点列表
 * @return 是否保存成功
 *  true 如果所有节点都保存成功
 *  false 如果有任何节点保存失败
 */
static bool UpsetNodeAttrs(wiz::DesignNode2Queue& queue, const WD::WDNode::Nodes& pNodes)
{
    auto& store = queue.getStore();
    for (const auto& pNode : pNodes)
    {
        if (pNode == nullptr)
            continue;
        auto nodeType = NodeBMType(*pNode);

        const auto& serializer = queue.getSerializer(nodeType);
        design::NodeAttrsRecord record;
        if (serializer.serializeNode(pNode, record))
            return false;

        store.upsetNodeAttrsRecord(record, pNode->uuid().toString(), nodeType, WD::store::IStore::FromLocal);
    }

    return true;
}

/**
* @brief 将节点关系保存到本地db
* @param queue 节点队列
* @param nodeType 队列节点类型
* @param treeAction 节点树数据
* @return 是否保存成功
*/
static bool UpsetNodeTree(wiz::DesignNode2Queue& queue
                          , const wiz::DesignNode2Queue::NodeType& nodeType
                          , const design::NodeTreeAction& treeAction)
{
    auto& store = queue.getStore();
    design::NodeTreeRecord record;
    WDUnused(treeAction);
    // TODO: NEWDC treeAction数据转换为record
    store.upsetNodeTreeRecord(record, nodeType, WD::store::IStore::FromLocal);

    return true;
}


static const size_t ChunkSize = 10000;
/**
* @brief 批量处理元素（支持数据结构vector）
* @tparam Element 元素类型
* @param elments 全部元素
* @param func 处理过程
* @return 是否处理成功
*/
template <typename Element>
static bool BatchExec(const std::vector<Element>& elments, std::function<bool(const std::vector<Element>&)> func)
{
    // 分批上传
    for (size_t i = 0; i < elments.size(); i += ChunkSize)
    {
        // 计算当前 chunk 的结束位置
        size_t end = i + ChunkSize;
        // 越界则到数组末尾为止
        if (end > elments.size())
            end = elments.size();

        // 创建一个新的 vector 来存储当前 chunk
        std::vector<Element> chunk(elments.begin() + i, elments.begin() + end);

        // 有任何一次提交失败就认为失败
        if (!func(chunk))
            return false;
    }
    return true;
}

ModelServer::ModelServer(WD::WDCore& core, Collaboration& collaboration)
    : _core(core)
      , _collaboration(collaboration)
{
}

ModelServer::~ModelServer()
{
}

bool ModelServer::projectInitialed() const
{
    // TODO: NEWDC
    return false;
}

bool ModelServer::push(WD::WDBMBase& mgr, const MSActions& msActions, bool toServer) const
{
    WDUnused(toServer);
    auto& queue = wiz::DesignNode2Queue::getInstance();
    wiz::DesignNode2Queue::NodeType nodeType = wiz::DesignNode2Queue::NodeType::DESIGN;
    if (mgr.name() == "Design")
        nodeType = wiz::DesignNode2Queue::NodeType::DESIGN;
    else if (mgr.name() == "Catalog")
        nodeType = wiz::DesignNode2Queue::NodeType::CATALOG;

    // 要推送的节点
    std::map<WD::WDUuid, WD::WDNode::SharedPtr> mapPushNode;
    for (const auto& [pWCloselyNodes, action] : msActions)
    {
        if (pWCloselyNodes.empty())
            continue;
        switch (action)
        {
        case NTA_Add:
            {
                // 节点数据
                for (const auto& pWCloselyNode : pWCloselyNodes)
                {
                    auto pNode = pWCloselyNode.lock();
                    if (pNode == nullptr)
                        continue;

                    mapPushNode[pNode->uuid()] = pNode;
                }
            }
            break;
        case NTA_Modify:
            {
                // 节点数据
                for (const auto& pWCloselyNode : pWCloselyNodes)
                {
                    auto pNode = pWCloselyNode.lock();
                    if (pNode == nullptr)
                        continue;

                    mapPushNode[pNode->uuid()] = pNode;
                }
            }
            break;
        case NTA_Remove:
            {
            }
            break;
        default:
            break;
        }
    }

    // 先推送节点，以生成remoteId
    WD::WDNode::Nodes pushNodes;
    for (const auto& [uuid, pNode] : mapPushNode)
    {
        pushNodes.push_back(pNode);
    }
    if (!BatchExec<WD::WDNode::SharedPtr>(pushNodes, [&queue, nodeType](const WD::WDNode::Nodes& nodes)-> bool
    {
        return queue.pushNodes(nodes, nodeType);
    }))
        return false;

    // 要推送的关系
    std::vector<design::NodeTreeAction> pushActions;
    for (const auto& [pWCloselyNodes, action] : msActions)
    {
        if (pWCloselyNodes.empty())
            continue;
        switch (action)
        {
        case NTA_Add:
            {
                // 节点关系
                design::NodeTreeAction treeAction;
                int64_t parentRid = 0;
                int64_t leftRid = 0;
                auto pFront = pWCloselyNodes.front().lock();
                if (pFront != nullptr)
                {
                    // 父id
                    auto pParent = pFront->parent();
                    if (pParent != nullptr)
                        parentRid = pParent->getRemoteId();

                    // left id
                    const auto& pLeft = pFront->prevBrother();
                    if (pLeft != nullptr)
                        leftRid = pLeft->getRemoteId();
                }
                treeAction.set_parentid(parentRid);
                treeAction.set_leftsiblingid(leftRid);
                for (const auto& pWNode : pWCloselyNodes)
                {
                    auto pNode = pWNode.lock();
                    if (pNode == nullptr)
                        continue;

                    treeAction.add_siblings(pNode->getRemoteId());
                }
                treeAction.set_flag(design::TreeActionFlag::TAF_INSERT);
                pushActions.push_back(treeAction);
            }
            break;
        case NTA_Modify:
            {
            }
            break;
        case NTA_Remove:
            {
                // 节点关系
                design::NodeTreeAction treeAction;
                int64_t parentRid = 0;
                auto pFront = pWCloselyNodes.front().lock();
                if (pFront != nullptr)
                {
                    // 父id
                    auto pParent = pFront->parent();
                    if (pParent != nullptr)
                        parentRid = pParent->getRemoteId();
                }
                treeAction.set_parentid(parentRid);
                for (const auto& pWNode : pWCloselyNodes)
                {
                    auto pNode = pWNode.lock();
                    if (pNode == nullptr)
                        continue;

                    treeAction.add_siblings(pNode->getRemoteId());
                }
                treeAction.set_flag(design::TreeActionFlag::TAF_DELETE);
                pushActions.push_back(treeAction);
            }
            break;
        default:
            break;
        }
    }

    // 推送节点关系
    if (!BatchExec<design::NodeTreeAction>(pushActions,
                                           [&queue, nodeType](const std::vector<design::NodeTreeAction>& trees)-> bool
                                           {
                                               return queue.pushNodeTreeActions(trees, nodeType);
                                           }))
        return false;

    // 推送完成后取消节点的F_Created和F_EdittedStatus标记
    WD::WDNode::Nodes newCreatedNodes;
    for (const auto& pNode : pushNodes)
    {
        if (pNode == nullptr)
            continue;

        auto flags = pNode->flags();
        if (flags.hasFlag(WD::WDNode::F_NewCreated))
        {
            newCreatedNodes.push_back(pNode);
            flags.removeFlag(WD::WDNode::F_NewCreated);
        }
        flags.removeFlag(WD::WDNode::Flag::F_Editted);
        pNode->setFlags(flags);
    }
    // 提交完成后是否自动释放
    auto& authMgr = mgr.authorityMgr();
    if (_collaboration.cfgInfo().auto_unlock)
    {
        bool bCancel = false;
        authMgr.unlockAll(bCancel);
        assert(bCancel == false);
        if (bCancel)
            LOG(ERROR) << "error unlock";
    }
    else
    {
        // 若不释放，则需要加锁新建的节点
        authMgr.lock(newCreatedNodes, true);
    }

    return true;
}

bool ModelServer::push(bool toServer) const
{
    LOG(INFO) << "commit begin";
    WDUnused(toServer);
    auto pBM = _core.currentBM();
    if (pBM == nullptr)
        return false;
    auto& queue = wiz::DesignNode2Queue::getInstance();
    auto pRoot = pBM->root();
    if (pRoot == nullptr)
        return false;
    const auto nodeType = NodeBMType(*pRoot);
    // 获取阻塞任务对象
    auto& blockTask = _core.blockingTask();

    // 要推送的节点
    WD::WDNode::Nodes pushNodes;
    this->getPushNodesRecursion(pRoot, pushNodes);
    if (!pushNodes.empty())
    {
        LOG(INFO) << "pushNodes begin（count:" << pushNodes.size() << "）";
        double progress = 0.0;
        auto progFactor = pushNodes.size() > ChunkSize ? (double)ChunkSize / (double)pushNodes.size() : 1.0;
        blockTask.setProgressText(WD::WDTs("ModelServerApi", "commit"), progress, 0);
        // 先推送节点，以生成remoteId
        if (!BatchExec<WD::WDNode::SharedPtr>(pushNodes, [&queue, nodeType, &blockTask, &progress, progFactor](const WD::WDNode::Nodes& nodes)-> bool
            {
                auto bOk = queue.pushNodes(nodes, nodeType);
                progress += 0.9 * progFactor;
                blockTask.setProgress(progress, 0);
                return bOk;
            }))
            return false;
        LOG(INFO) << "pushNodes end";
    }
    // 要推送的关系
    std::vector<design::NodeTreeAction> pushActions;
    this->getPushTreeActionsRecursion(pRoot, pushActions);
    // 暂时从申领管理中获取删除的节点
    auto& authMgr = pBM->authorityMgr();
    for (const auto& CBDeleteNode : authMgr.delNodes())
    {
        const auto& pNode = CBDeleteNode.second.lock();
        if (pNode == nullptr)
            continue;
        design::NodeTreeAction treeAction;
        auto pParent = pNode->parent();
        if (pParent == nullptr)
            continue;
        treeAction.set_parentid(pParent->getRemoteId());
        treeAction.add_siblings(pNode->getRemoteId());
        treeAction.set_flag(design::TreeActionFlag::TAF_DELETE);
        pushActions.push_back(treeAction);
    }
    blockTask.setProgress(0.9, 0);
    LOG(INFO) << "pushTrees begin（count:" << pushActions.size() << "）";
    // 打印日志
    for (const auto& pushAction : pushActions)
    {
        std::string infoStr = "TreeActionInfo (";
        infoStr += std::to_string(pushAction.parentid()) + " [";
        for (int i = 0; i < pushAction.siblings_size(); ++i)
        {
            infoStr += std::to_string(pushAction.siblings(i));
            if (i != pushAction.siblings_size() - 1)
                infoStr += ", ";
        }
        infoStr += "])";
        LOG(INFO) << infoStr;
    }
    // 推送节点关系
    if (!BatchExec<design::NodeTreeAction>(pushActions,
                                           [&queue, nodeType](const std::vector<design::NodeTreeAction>& trees)-> bool
                                           {
                                               return queue.pushNodeTreeActions(trees, nodeType);
                                           }))
        return false;
    LOG(INFO) << "pushTrees end";

    // 推送完成后取消节点的F_Created和F_EdittedStatus标记
    WD::WDNode::Nodes newCreatedNodes;
    for (const auto& pNode : pushNodes)
    {
        if (pNode == nullptr)
            continue;

        auto flags = pNode->flags();
        if (flags.hasFlag(WD::WDNode::F_NewCreated))
        {
            newCreatedNodes.push_back(pNode);
            flags.removeFlag(WD::WDNode::F_NewCreated);
        }
        flags.removeFlag(WD::WDNode::Flag::F_Editted);
        pNode->setFlags(flags);
    }
    // 提交完成后是否自动释放
    if (_collaboration.cfgInfo().auto_unlock)
    {
        bool bCancel = false;
        authMgr.unlockAll(bCancel);
        assert(bCancel == false);
        if (bCancel)
            LOG(ERROR) << "error unlock";
    }
    else
    {
        // 若不释放，则需要加锁新建的节点
        authMgr.lock(newCreatedNodes, true);
    }

    // 清空申领管理中的删除节点列表
    authMgr.delNodes().clear();

    // TODO: 移动的节点暂时未处理（应该分离成新增和删除）

    LOG(INFO) << "commit end";
    blockTask.setProgress(1.0, 0);
    return true;
}

bool ModelServer::pull()
{
    bool needUpdate = false;

    // 获取批量消息
    std::vector<wiz::KafkaMessageWrapperPtr> messages;
    auto& kafkaService = wiz::DesignKafkaService::getInstance();
    kafkaService.popMessages(messages, 100); // 一次最多获取100条消息
    for (auto& m : messages)
    {
        auto tlv = m->tlv;
        if (tlv.getVersion() == 0)
        {
            auto vs = tlv.parseV0(m->message.data(), m->message.size());
            for (const auto& v : vs)
            {
                if (TLVHandler(v.first, v.second, tlv.getType(), tlv.getNodeType()))
                    needUpdate = true;
            }
        }
        else
        {
            if (TLVHandler(tlv.getData(), tlv.getLength(), tlv.getType(), tlv.getNodeType()))
                needUpdate = true;
        }
    }

    return needUpdate;
}

void ModelServer::updateLocalCahche()
{
    std::lock_guard<std::mutex> lock(_mutex);

    // 更新到缓存
    // 此时的消息已由服务器保证顺序，保持数据最终一致性
    for (auto relationItr = _relations.begin(); relationItr != _relations.end();)
    {
        const auto& [parentRid, chidrenRids] = *relationItr;
        // 父节点
        WD::WDNode::SharedPtr pParent = nullptr;
        if (auto it = _sceneNodeData.find(parentRid); it != _sceneNodeData.end())
            pParent = it->second;
        if (pParent == nullptr)
        {
            relationItr = _relations.erase(relationItr);
            continue;
        }
        auto pCurBM = pParent->getBMBase();
        if (pCurBM == nullptr)
        {
            relationItr = _relations.erase(relationItr);
            continue;
        }

        // 收集父节点于当前场景树上的子节点列表(pair-second是用来标识有没有在新的子节点列表中找到，便于删除节点)
        std::map<uint64_t, std::pair<WD::WDNode::SharedPtr, bool>> oldChilren;
        for (const auto& pChild : pParent->children())
        {
            if (pChild == nullptr)
                continue;
            oldChilren[pChild->getRemoteId()] = std::make_pair(pChild, false);
        }

        // 为了使goto使用编译通过，所以在使用之前进行初始化
        WD::WDNode::SharedPtr pNext = nullptr;

        // 优先处理增删，以便接下来设置正确的节点顺序
        for (const auto& newChild : chidrenRids)
        {
            // 在当前子节点列表中没有找到，即为新增的节点
            if (auto it = oldChilren.find(newChild); it == oldChilren.end())
            {
                // 在缓存的更新节点中查询新增节点
                auto addNodeItr = _updateNode.find(newChild);
                if (addNodeItr != _updateNode.end())
                {
                    // 先收集引用（此时不需要父子关系），懒加载考虑调整此处
                    pCurBM->updateRefs({ addNodeItr->second });

                    // 先将新增节点挂载父节点下，此时不排序
                    pCurBM->setParent(addNodeItr->second, pParent);
                    addNodeItr->second->update();

                    _sceneNodeData[addNodeItr->first] = addNodeItr->second;

                    // 当前缓存的更新节点已处理，从缓存中移除
                    _updateNode.erase(addNodeItr);
                }
                else
                {
                    // 没有在缓存中找到新增节点数据，直接继续下一次循环
                    goto ContinueThisTime;
                }
            }
            // 若找到了则标识，且更新节点数据
            else
            {
                it->second.second = true;

                // 找到则更新节点数据
                auto updateNodeItr = _updateNode.find(newChild);
                if (updateNodeItr != _updateNode.end())
                {
                    // 更新节点
                    it->second.first->copy(updateNodeItr->second.get());
                    it->second.first->update();

                    // 当前缓存的更新节点已处理，从缓存中移除
                    _updateNode.erase(updateNodeItr);
                }
                else
                {
                    // 没有在缓存中找到更新节点数据，直接继续下一次循环
                    //goto ContinueThisTime;
                }
            }
        }
        // 遍历当前场景中的子节点列表，标识为未找到的即是需要删除的节点
        for (const auto& oldChild : oldChilren)
        {
            if (!oldChild.second.second)
            {
                pCurBM->destroy(oldChild.second.first);
                _sceneNodeData.erase(oldChild.first);
            }
        }

        // 此处为降低复杂度，蛮力设置所有子节点的关系。TODO: 提供效率更高的排序算法
        for (auto itr = chidrenRids.rbegin(); itr != chidrenRids.rend(); ++itr)
        {
            WD::WDNode::SharedPtr pNode = nullptr;
            if (auto it = _sceneNodeData.find(*itr); it != _sceneNodeData.end())
            {
                pNode = it->second;
            }
            else
            {
                // 没有在缓存中找到节点数据，直接继续下一次循环
                goto ContinueThisTime;
            }

            pCurBM->setParent(pNode, pParent, pNext);

            pNext = pNode;
        }
        // 更新父节点
        pParent->triggerUpdate();

        relationItr = _relations.erase(relationItr);
        continue;

        // 直接继续下一次循环
    ContinueThisTime :
        relationItr++;
    }

    // 如果缓存的更新节点还有，则直接更新节点
    for (auto itr = _updateNode.begin(); itr != _updateNode.end();)
    {
        if (auto it = _sceneNodeData.find(itr->first); it != _sceneNodeData.end())
        {
            const auto& pNode = it->second;
            auto pCurBM = pNode->getBMBase();
            if (pCurBM == nullptr)
                continue;
            if (pNode != nullptr)
            {
                // 更新节点
                pNode->copy(itr->second.get());
                pNode->update();

                // 先收集引用（此时不需要父子关系），懒加载考虑调整此处
                // TODO: 此处是否应提供不递归更新节点引用的接口，避免重复更新损失效率
                pCurBM->updateRefs({ it->second });

                // 当前缓存的更新节点已处理，从缓存中移除
                itr = _updateNode.erase(itr);
                continue;
            }
        }
        itr++;
    }

    _core.needRepaint();
}

void ModelServer::cacheNodeData()
{
    auto pCataRoot = _core.getBMCatalog().root();
    if (pCataRoot != nullptr)
    {
        WD::WDNode::RecursionHelpter(*pCataRoot, [this](WD::WDNode& node)
            {
                _sceneNodeData[node.getRemoteId()] = WD::WDNode::ToShared(&node);
            });
    }
    auto pDesiRoot = _core.getBMDesign().root();
    if (pDesiRoot != nullptr)
    {
        WD::WDNode::RecursionHelpter(*pDesiRoot, [this](WD::WDNode& node)
            {
                _sceneNodeData[node.getRemoteId()] = WD::WDNode::ToShared(&node);
            });
    }
}

bool ModelServer::clear(std::string_view moduleName)
{
    if (moduleName.empty())
    {
        auto pBM = _core.currentBM();
        if (pBM != nullptr)
            moduleName = pBM->name();
    }
    wiz::DesignNode2Queue::NodeType nodeType = wiz::DesignNode2Queue::NodeType::DESIGN;
    if (moduleName == "Design")
        nodeType = wiz::DesignNode2Queue::NodeType::DESIGN;
    else if (moduleName == "Catalog")
        nodeType = wiz::DesignNode2Queue::NodeType::CATALOG;

    auto& queue = wiz::DesignNode2Queue::getInstance();

    // 组织节点关系
    design::NodeTreeAction treeAction;
    treeAction.set_parentid(0);
    treeAction.set_flag(design::TreeActionFlag::TAF_UPDATE_ALL);
    return queue.pushNodeTreeAction(treeAction, nodeType);
}

void ModelServer::initAuthorityCallback()
{
    auto pBM = _core.currentBM();
    if (pBM != nullptr)
    {
        auto& authMgr = pBM->authorityMgr();
        authMgr.funcLockBefore() = std::bind(&ModelServer::lockBefore, this, std::placeholders::_1);
        authMgr.funcUnlockBefore() = std::bind(&ModelServer::unlockBefore, this, std::placeholders::_1, std::placeholders::_2);
    }
}

void ModelServer::getPushNodesRecursion(WD::WDNode::SharedPtr pNode, WD::WDNode::Nodes& result) const
{
    if (pNode == nullptr)
        return;

    if (pNode->flags().hasFlag(WD::WDNode::Flag::F_NewCreated))
    {
        WD::WDNode::RecursionHelpter(*pNode, [&result](WD::WDNode& node)
        {
            result.push_back(WD::WDNode::ToShared(&node));
        });
        return;
    }
    if (pNode->flags().hasFlag(WD::WDNode::Flag::F_Editted))
    {
        result.push_back(pNode);
    }

    // 递归子节点
    const auto& pChildren = pNode->children();
    for (const auto& pChild : pChildren)
    {
        getPushNodesRecursion(pChild, result);
    }
}

void ModelServer::getPushTreeActionsRecursion(WD::WDNode::SharedPtr pNode,
                                              std::vector<design::NodeTreeAction>& result) const
{
    if (pNode == nullptr)
        return;

    if (pNode->flags().hasFlag(WD::WDNode::Flag::F_NewCreated))
    {
        // 添加新增pNode的节点关系
        {
            // 节点关系
            design::NodeTreeAction treeAction;
            int64_t parentRid = 0;
            int64_t leftRid = 0;
            // 父id
            auto pParent = pNode->parent();
            if (pParent != nullptr)
                parentRid = pParent->getRemoteId();
            // left id
            const auto& pLeft = pNode->prevBrother();
            if (pLeft != nullptr)
                leftRid = pLeft->getRemoteId();
            treeAction.set_parentid(parentRid);
            treeAction.set_leftsiblingid(leftRid);
            treeAction.add_siblings(pNode->getRemoteId());
            treeAction.set_flag(design::TreeActionFlag::TAF_INSERT);
            result.push_back(treeAction);
        }
        // 递归添加新增的节点关系
        {
            WD::WDNode::RecursionHelpter(*pNode, [&result](WD::WDNode& node)
            {
                const auto& pChildren = node.children();
                if (pChildren.empty())
                    return;
                // 节点关系
                design::NodeTreeAction treeAction;
                int64_t parentRid = node.getRemoteId();
                int64_t leftRid = 0;
                treeAction.set_parentid(parentRid);
                treeAction.set_leftsiblingid(leftRid);
                for (const auto& pChild : pChildren)
                {
                    if (pChild == nullptr)
                        continue;
                    treeAction.add_siblings(pChild->getRemoteId());
                }
                treeAction.set_flag(design::TreeActionFlag::TAF_INSERT);
                result.push_back(treeAction);
            });
        }
        return;
    }

    // 递归子节点
    const auto& pChildren = pNode->children();
    for (const auto& pChild : pChildren)
    {
        getPushTreeActionsRecursion(pChild, result);
    }
}

bool ModelServer::TLVHandler(const void* data, int size, int msgType, design::NodeType nodeType)
{
    std::lock_guard<std::mutex> lock(_mutex);
    bool needUpdate = false;
    auto & queue = wiz::DesignNode2Queue::getInstance();
    if (msgType == 1)
    {
        // 节点数据
        design::NodeAttrsRecord record;
        auto ok = record.ParseFromArray(data, size);
        if (!ok) return false;
        // 反序列化成 WDNode
        auto pNode = WD::WDNode::MakeShared();
        queue.deserializeNode(pNode, nodeType, record);
        auto uuid = pNode->uuid().toString();
        LOG(INFO) << pNode->name() << "|" << uuid << "|traceId: " << record.traceid();

        // 保存数据库
        queue.getStore().upsetNodeAttrsRecord(record, uuid, nodeType, WD::store::IStore::StoreFrom::FromServer);

        // 根据userId区分是否需要同时更新到场景
        const auto& userId = record.additionalinfo().checkoutuser();
        if (userId != _collaboration.userInfo().id)
        {
            _updateNode[pNode->getRemoteId()] = pNode;
            needUpdate = true;
        }
    }
    else
    {
        // 节点关系
        design::NodeTreeRecord treeRecord;
        auto ok = treeRecord.ParseFromArray(data, size);
        if (!ok) return false;

        NSNodeTreeRecord::Log(treeRecord);

        // 保存数据库
        queue.getStore().upsetNodeTreeRecord(treeRecord, nodeType, WD::store::IStore::StoreFrom::FromServer);

        // 根据userId区分是否需要同时更新到场景
        const auto& userId = treeRecord.additionalinfo().checkoutuser();
        if (userId != _collaboration.userInfo().id)
        {
            auto pRid = NSNodeTreeRecord::Parent(treeRecord);
            _relations[pRid] = NSNodeTreeRecord::Children(treeRecord);
            needUpdate = true;
        }
    }

    return needUpdate;
}

static std::string CurrentClassification(WD::WDCore& core)
{
    auto pBM = core.currentBM();
    if (pBM == nullptr)
        return std::string();

    if (pBM->name() == "Design")
        return Classification_Design;
    else if (pBM->name() == "Catalog")
        return Classification_Catalog;

    return std::string();
}
std::set<int64_t> ModelServer::lockBefore(const std::vector<WD::WDBMAuthorityMgr::LockItem>& items)
{
    std::set<int64_t> result;

    auto projectInfo = DesignProjectInfo(_collaboration);
    projectInfo.set_classification(CurrentClassification(_core));
    // 将加锁节点分批，分成带子孙节点标记和不带子孙节点标记的两批节点（这里是为了减少数据传输量）
    design::ProjectNodesTree projNodesTreeSelf;
    design::ProjectNodesTree projNodesTreeRecu;
    *projNodesTreeSelf.mutable_projectinfo() = projectInfo;
    *projNodesTreeRecu.mutable_projectinfo() = projectInfo;
    design::CheckInOutNode checkInOutNodeSelf;
    design::CheckInOutNode checkInOutNodeRecu;
    checkInOutNodeSelf.set_checkout(CheckOut_Flag);
    checkInOutNodeRecu.set_checkout(CheckOut_Flag);
    checkInOutNodeSelf.set_flag(Self_Flag);
    checkInOutNodeRecu.set_flag(Descendants_Flag);
    design::VectorInt64 selfIds;
    design::VectorInt64 recuIds;
    for (const auto& [pWNode, inherit] : items)
    {
        auto pNode = pWNode.lock();
        if (pNode == nullptr)
            continue;
        auto rid = pNode->getRemoteId();
        if (inherit)
            recuIds.add_vec_int64(rid);
        else
            selfIds.add_vec_int64(rid);
    }
    *checkInOutNodeSelf.mutable_ids() = selfIds;
    *checkInOutNodeRecu.mutable_ids() = recuIds;
    *projNodesTreeSelf.mutable_checkinoutnode() = checkInOutNodeSelf;
    *projNodesTreeRecu.mutable_checkinoutnode() = checkInOutNodeRecu;

    // 尝试加锁
    const auto& checkResSelf = wiz::DesignServiceClient::getInstance().CheckInOut(projNodesTreeSelf);
    const auto& checkResRecu = wiz::DesignServiceClient::getInstance().CheckInOut(projNodesTreeRecu);

    // 报告错误
    if (checkResSelf.failedrecord_size() != 0 || checkResRecu.failedrecord_size() != 0)
    {
        // 准备查询表，查询失败一定是在签出的节点列表里面
        std::map<int64_t, WD::WDNode::SharedPtr> cacheCheckOutNodes;
        for (const auto& [pWNode, inherit] : items)
        {
            auto pNode = pWNode.lock();
            if (pNode == nullptr)
                continue;
            if (inherit)
            {
                WD::WDNode::RecursionHelpter(*pNode, [&cacheCheckOutNodes](WD::WDNode& node) {
                        cacheCheckOutNodes[node.getRemoteId()] = WD::WDNode::ToShared(&node);
                    });
            }
            else
            {
                cacheCheckOutNodes[pNode->getRemoteId()] = pNode;
            }
        }
        // 名称查询
        auto funcQueryName = [&cacheCheckOutNodes](int64_t rid) -> std::optional<std::string>
            {
                auto pItr = cacheCheckOutNodes.find(rid);
                if (pItr == cacheCheckOutNodes.end())
                    return std::nullopt;

                const auto& pNode = pItr->second;
                if (pNode == nullptr)
                    return std::nullopt;
                return pNode->name();
            };
        std::string errorInfo = WD::WDTs("Collaboration", "Fail to checkOut");
        // 提示信息拼接
        auto funcInfoSplice = [&errorInfo, &funcQueryName](const ::google::protobuf::RepeatedPtrField<::design::CheckInOutFailRecord>& failedRecords)
            {
                for (const auto& failedRecord : failedRecords)
                {
                    if (failedRecord.additionalinfo().status() == 0) // 成功（节点为签出状态且签出人是本人）
                    {
                    }
                    else if (failedRecord.additionalinfo().status() == 1) // 失败（节点为签出状态且签出人不是本人）
                    {
                        // "节点xxx已被用户xxx申领;"
                        errorInfo += "\n";
                        errorInfo += WD::WDTs("Collaboration", "Node");
                        auto resName = funcQueryName(failedRecord.id());
                        if (resName)
                            errorInfo += resName.value();
                        else
                            errorInfo += WD::WDTs("Collaboration", "Unkown node");

                        errorInfo += WD::WDTs("Collaboration", "Has been used by the user");
                        errorInfo += failedRecord.additionalinfo().checkoutuser();
                        errorInfo += WD::WDTs("Collaboration", "CheckOut");
                    }
                    else if (failedRecord.additionalinfo().status() == 2) // 失败（节点不存在）
                    {
                        // "节点xxx不存在;"
                        errorInfo += "\n";
                        errorInfo += WD::WDTs("Collaboration", "Node");
                        auto resName = funcQueryName(failedRecord.id());
                        if (resName)
                            errorInfo += resName.value();
                        else
                            errorInfo += WD::WDTs("Collaboration", "Unkown node");

                        errorInfo += WD::WDTs("Collaboration", "Don't exist");
                    }
                }
            };
        funcInfoSplice(checkResSelf.failedrecord());
        funcInfoSplice(checkResRecu.failedrecord());

        WD_ERROR(errorInfo);
    }

    // 成功签出的节点
    for (const auto& successId : checkResSelf.successids().vec_int64())
    {
        result.insert(successId);
    }
    for (const auto& successId : checkResRecu.successids().vec_int64())
    {
        result.insert(successId);
    }

    return result;
}

/**
 * @brief 是否有数据未提交
 */
static bool HasNotSubmit(WD::WDCore& core)
{
    auto pBM = core.currentBM();
    if (pBM == nullptr)
        return false;
    auto pRoot = pBM->root();
    if (pRoot == nullptr)
        return false;

    bool hasNotSubmit = false;
    WD::WDNode::RecursionHelpterR(*pRoot, [&hasNotSubmit](WD::WDNode& node) 
        {
            // 有编辑或者新增标记则视为有数据未提交
            const auto& flags = node.flags();
            if (flags.hasFlag(WD::WDNode::F_NewCreated) || flags.hasFlag(WD::WDNode::F_Editted))
            {
                hasNotSubmit = true;
                return true;
            }

            return false;
        });

    if (hasNotSubmit)
        return true;

    // 判断是否有删除的节点
    auto& authMgr = pBM->authorityMgr();
    if (!authMgr.delNodes().empty())
        return true;
    // TODO: 判断是否有移动的节点

    return false;
}
std::set<int64_t> ModelServer::unlockBefore(const std::vector<WD::WDBMAuthorityMgr::LockItem>& items, bool& bCancelUnlock)
{
    WDUnused(bCancelUnlock);
    std::set<int64_t> result;

    // 有数据未提交，则提示用户需提交数据
    if (HasNotSubmit(_core))
    {
        WD_ERROR_T("Collaboration", "Something has not been submitted");
        return result;
    }

    auto projectInfo = DesignProjectInfo(_collaboration);
    projectInfo.set_classification(CurrentClassification(_core));
    // 将解锁节点分批，分成带子孙节点标记和不带子孙节点标记的两批节点（这里是为了减少数据传输量）
    design::ProjectNodesTree projNodesTreeSelf;
    design::ProjectNodesTree projNodesTreeRecu;
    *projNodesTreeSelf.mutable_projectinfo() = projectInfo;
    *projNodesTreeRecu.mutable_projectinfo() = projectInfo;
    design::CheckInOutNode checkInOutNodeSelf;
    design::CheckInOutNode checkInOutNodeRecu;
    checkInOutNodeSelf.set_checkout(CheckIn_Flag);
    checkInOutNodeRecu.set_checkout(CheckIn_Flag);
    checkInOutNodeSelf.set_flag(Self_Flag);
    checkInOutNodeRecu.set_flag(Descendants_Flag);
    design::VectorInt64 selfIds;
    design::VectorInt64 recuIds;
    for (const auto& [pWNode, inherit] : items)
    {
        auto pNode = pWNode.lock();
        if (pNode == nullptr)
            continue;
        auto rid = pNode->getRemoteId();
        if (inherit)
            recuIds.add_vec_int64(rid);
        else
            selfIds.add_vec_int64(rid);
    }
    *checkInOutNodeSelf.mutable_ids() = selfIds;
    *checkInOutNodeRecu.mutable_ids() = recuIds;
    *projNodesTreeSelf.mutable_checkinoutnode() = checkInOutNodeSelf;
    *projNodesTreeRecu.mutable_checkinoutnode() = checkInOutNodeRecu;

    // 尝试解锁
    const auto& checkResSelf = wiz::DesignServiceClient::getInstance().CheckInOut(projNodesTreeSelf);
    const auto& checkResRecu = wiz::DesignServiceClient::getInstance().CheckInOut(projNodesTreeRecu);

    // 报告错误
    if (checkResSelf.failedrecord_size() != 0 || checkResRecu.failedrecord_size() != 0)
    {
        // 目前可能会失败的情况只有一种，签入的节点已经被别人签出了，这种情况在客户端的逻辑中已经被排除，所以不考虑
        LOG(ERROR) << "error checkIn";
    }

    // 成功签出的节点
    for (const auto& successId : checkResSelf.successids().vec_int64())
    {
        result.insert(successId);
    }
    for (const auto& successId : checkResRecu.successids().vec_int64())
    {
        result.insert(successId);
    }

    return result;
}
