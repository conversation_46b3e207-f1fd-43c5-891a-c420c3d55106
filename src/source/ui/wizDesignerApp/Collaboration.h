#pragma once

#include "UiInterface/ICollaboration.h"

#include "WDRapidjson.h"
#include "service/ConfigParser.h"
#include "store/db_store/DbStore.h"
#include "service/DesignNode2Queue.h"

#include "core/businessModule/WDBMAuthorityMgr.h"

#include <thread>
#include <mutex>

#include <QObject>

#include "service/DesignKafkaData.h"

/**
 * @brief 协同相关数据以及接口
*/
class Collaboration : public ICollaboration
{
    // 引擎对象引用
    WD::WDCore& _core;
    // 登录服务接口
    ILoginServer* _pLoginServer;
    // 申领服务接口
    IModelServer* _pModelServer;
    // 协同用户信息
    CollabUserInfo _userInfo;
    // 协同项目信息
    CollabProjectInfo _projectInfo;
    // 协同配置信息
    CollabCfgInfo _cfgInfo;
    // 是否激活协同服务
    bool _isCollaboration;

public:
    explicit Collaboration(WD::WDCore& core);
    ~Collaboration();

    ILoginServer& loginServer() override
    {
        return *_pLoginServer;
    }

    IModelServer& modelServer() override
    {
        return *_pModelServer;
    }

    const CollabUserInfo& userInfo() const override
    {
        return _userInfo;
    }

    CollabUserInfo& userInfo() override
    {
        return _userInfo;
    }

    const CollabProjectInfo& projectInfo() const override
    {
        return _projectInfo;
    }

    CollabProjectInfo& projectInfo() override
    {
        return _projectInfo;
    }

    const CollabCfgInfo& cfgInfo() const override
    {
        return _cfgInfo;
    }

    bool actived() const override
    {
        return _isCollaboration;
    }

private:
    /**
     * @brief 初始化
     */
    void init();
    /**
    * @brief 读取协同配置
    */
    void readConfig();
};

WD_NAMESPACE_BEGIN
class WDNodeCacheByType;
WD_NAMESPACE_END

/**
* @brief 登录服务接口
*/
class LoginServer : public ILoginServer
{
    WD::WDCore& _core;
    Collaboration& _collaboration;

    // 节点缓存（后续考虑直接在打开项目时构建节点树，此变量会删除）
    WD::WDNodeCacheByType& _nodeCache;

public:
    LoginServer(WD::WDCore& core, Collaboration& collaboration);
    virtual ~LoginServer();

    LoginUserConfig login(std::string_view userName, std::string_view password) const override;
    /**
     * 下载minio数据文件并导入到本地
     * @param serviceConfig
     * @param store
     */
    void downloadFromMinioAndUpsetDB(wiz::ServiceConfig serviceConfig
                                    , WD::store::IStore& store
                                    , std::string_view projCode) const;
    bool openProject(const UserCfgProj& project, std::string_view userRole) override;

    void load() const override;

private:
    LoginUserConfig parseUserConfig(std::string_view str) const;

    std::string projDbPath();

    friend class Collaboration;
};

/**
 * @brief 模型服务接口
*/
class ModelServer : public IModelServer
{
    WD::WDCore& _core;
    Collaboration& _collaboration;

    // 场景中的数据map
    std::map<int64_t, WD::WDNode::SharedPtr> _sceneNodeData;
    // 缓存的节点数据
    std::map<int64_t, WD::WDNode::SharedPtr> _updateNode;
    // 缓存的节点关系
    std::map<int64_t, std::vector<int64_t>> _relations;

    // 线程锁
    std::mutex _mutex;

public:
    ModelServer(WD::WDCore& core, Collaboration& collaboration);
    virtual ~ModelServer();

    bool projectInitialed() const override;

    bool push(WD::WDBMBase& mgr, const MSActions& msActions, bool toServer) const override;
    bool push(bool toServer) const override;
    bool pull() override;
    void updateLocalCahche() override;
    void cacheNodeData() override;
    bool clear(std::string_view moduleName) override;

public:
    void initAuthorityCallback() override;

private:
    /**
     * @brief 递归获取要推送的节点数据
     * @param pNode 节点
     * @param result 结果
     */
    void getPushNodesRecursion(WD::WDNode::SharedPtr pNode, WD::WDNode::Nodes& result) const;
    /**
    * @brief 递归获取要推送的节点关系
    * @param pNode 节点
    * @param result 结果
    */
    void getPushTreeActionsRecursion(WD::WDNode::SharedPtr pNode, std::vector<design::NodeTreeAction>& result) const;

    bool TLVHandler(const void* data, int size, int msgType, design::NodeType nodeType);

private:
    /****************************************************签入签出相关回调（TODO）******************************************************/
    std::set<int64_t> lockBefore(const std::vector<WD::WDBMAuthorityMgr::LockItem>& items);
    std::set<int64_t> unlockBefore(const std::vector<WD::WDBMAuthorityMgr::LockItem>& items, bool& bCancelUnlock);

    friend class Collaboration;
};

/**
* @brief 安全获取Json值，至少保证不会崩溃
* @tparam T 获取的值类型
* @param value jsonValue(为了兼容GetObj传入非const引用)
* @param name 名称
* @param outValue 输出的值
* @return 是否获取成功，如果 jsonValue不包含对应名称的值或者值类型不匹配，则返回false
*/
template <typename T>
bool GetJsonValueSafely(WD::JsonValue& value, const std::string_view& name, T& outValue)
{
    const char* tName = name.data();
    if (!value.IsObject() || !value.HasMember(tName))
        return false;

    auto& tValue = value[tName];
    if (tValue.IsNull())
        return false;

    if constexpr (std::is_same_v<T, bool>)
    {
        outValue = tValue.GetBool();
        return true;
    }
    else if constexpr (std::is_same_v<T, int>)
    {
        outValue = tValue.GetInt();
        return true;
    }
    else if constexpr (std::is_same_v<T, int64_t>)
    {
        outValue = tValue.GetInt64();
        return true;
    }
    else if constexpr (std::is_same_v<T, unsigned int>)
    {
        outValue = tValue.GetUint();
        return true;
    }
    else if constexpr (std::is_same_v<T, uint64_t>)
    {
        outValue = tValue.GetUint64();
        return true;
    }
    else if constexpr (std::is_same_v<T, float>)
    {
        outValue = tValue.GetFloat();
        return true;
    }
    else if constexpr (std::is_same_v<T, double>)
    {
        outValue = tValue.GetDouble();
        return true;
    }
    else if constexpr (std::is_same_v<T, std::string>)
    {
        outValue = tValue.GetString();
        return true;
    }
    else if constexpr (std::is_same_v<T, WD::JsonValue>)
    {
        outValue = tValue;
        return true;
    }
    else
    {
        return false;
    }
}

/**
* @brief 安全获取Json的属性值，至少保证不会崩溃
* @tparam T 获取的值类型
* @param value json对象
* @param key 属性名称
* @return 结果, 如果获取失败，则返回默认值
*/
template <typename T>
T GetJsonValueSafely(WD::JsonValue& value, const std::string_view& name)
{
    T ret;
    GetJsonValueSafely(value, name, ret);
    return ret;
}
