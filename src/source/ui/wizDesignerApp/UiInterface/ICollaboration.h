#pragma once

#include    "core/WDCore.h"
#include    "core/node/WDNode.h"

class ILoginServer;
class IModelServer;
/**
 * @brief 协同相关数据以及接口
*/
class ICollaboration
{
public:
    /**
     * @brief 协同用户信息
     */
    struct CollabUserInfo
    {
        // 用户ID
        std::string id;
        // 用户密码
        std::string password;
        // 登录角色code
        std::string roleCode;
        // 用户名称
        std::string name;
        // 拥有元件库权限
        bool catalogPerm = false;
    };
    /**
    * @brief 协同项目信息
    */
    struct CollabProjectInfo
    {
        // 项目Id
        uint64_t id;
        // 项目名称
        std::string name;
        // 项目code
        std::string code;
    };
    /**
    * @brief 协同配置信息
    */
    struct CollabCfgInfo
    {
        // 数据库地址
        std::string db;
        // grpc地址
        std::string grpc;
        // 实时拉取数据
        bool immediately_pull = true;
        // 自动解锁
        bool auto_unlock = true;
    };

public:
    /**
     * @brief 登录服务相关接口
    */
    virtual ILoginServer& loginServer() = 0;
    /**
    * @brief 模型服务相关接口
    */
    virtual IModelServer& modelServer() = 0;

    /**
     * @brief 获取用户信息
    */
    virtual const CollabUserInfo& userInfo() const = 0;
    virtual CollabUserInfo& userInfo() = 0;
    /**
     * @brief 获取项目信息
    */
    virtual const CollabProjectInfo& projectInfo() const = 0;
    virtual CollabProjectInfo& projectInfo() = 0;
    /**
    * @brief 获取配置信息
    */
    virtual const CollabCfgInfo& cfgInfo() const = 0;

    /**
     * @brief 是否激活协同服务
    */
    virtual bool actived() const = 0;
};

/**
* @brief 登录服务接口
*/
class ILoginServer
{
public:
    static constexpr const char* LoginUserRole_Admin = "Admin";
    static constexpr const char* LoginUserRole_PM = "PM";
    static constexpr const char* LoginUserRole_MM = "MM";
    static constexpr const char* LoginUserRole_Designer = "Designer";
    struct UserCfgRole
    {
        // 角色名称
        std::string name;
        // 项目编码
        std::string code;
    };
    struct UserCfgProj
    {
        // 项目id
        uint64_t    id;
        // 项目名称
        std::string name;
        // 项目编码
        std::string code;
        // 角色列表
        std::vector<UserCfgRole> roles;
    };
    /**
     * @brief 登录用户配置
     */
    using LoginUserConfig = std::vector<UserCfgProj>;

public:
    virtual ~ILoginServer()
    {

    }
public:
    /**
     * @brief 用户登录
     * @param userName 用户名称
     * @param password 登录密码
     * @return 登录用户配置
     */
    virtual LoginUserConfig login(std::string_view userName, std::string_view password) const = 0;
    /**
     * @brief 打开工程项目
     * @param code 项目编码
     * @param userRole 用户角色
     * @return 打开是否成功
     */
    virtual bool openProject(const UserCfgProj& project, std::string_view userRole) = 0;

    /**
     * @brief 加载登录时拉取的数据（后续考虑直接在打开项目时构建节点树，此接口或删除）
     */
    virtual void load() const = 0;
};

/**
 * @brief 模型服务接口
*/
class IModelServer
{
public:
    /**
     * @brief 节点操作
     */
    enum NodeTreeAction
    {
        // 新增
        NTA_Add = 0,
        // 数据修改（仅修改节点数据，不更改节点关系）
        NTA_Modify,
        // 节点移动（仅更改节点关系，不修改节点数据）节点移动会转化为一个新增action和一个删除action
        //NTA_Move,
        // 删除（节点数据不会自动删除，只删除节点关系）
        NTA_Remove
    };
    /**
     * @brief 模型服务节点操作
     */
    struct MSAction
    {
        // 连续的节点的弱指针
        std::vector<WD::WDNode::WeakPtr>    pWCloselyNodes;
        // 节点操作
        NodeTreeAction                      action;

        MSAction()
        {
            action = NTA_Add;
        }
        MSAction(std::vector<WD::WDNode::SharedPtr> v1, NodeTreeAction v2)
        {
            for (const auto& pNode : v1)
            {
                pWCloselyNodes.push_back(pNode);
            }
            action = v2;
        }
    };
    using MSActions = std::vector<MSAction>;

public:
    virtual ~IModelServer()
    {

    }
public:
    /**
     * @brief 校验项目是否已经被初始化
    */
    virtual bool projectInitialed() const = 0;

    /**
     * @brief 推送指定数据，不会递归
     * @param mgr 推送数据的模块管理，不一定是当前模块
     * @param msActions 推送数据
     * @param toServer 是否同时推送至服务器，默认true
     * @return 推送是否成功
     */
    virtual bool push(WD::WDBMBase& mgr, const MSActions& msActions, bool toServer = true) const = 0;
    /**
     * @brief 推送需要推送的数据
     * @param toServer 是否同时推送至服务器，默认true
     * @return 推送是否成功
    */
    virtual bool push(bool toServer = true) const = 0;
    /**
    * @brief 从服务器拉取数据（先保存到本地db，再更新到场景缓存）
    * @return 拉取是否成功
    */
    virtual bool pull() = 0;
    /**
    * @brief 更新缓存数据（每次拉取数据更新）
    */
    virtual void updateLocalCahche() = 0;
    virtual void cacheNodeData() = 0;
    /**
    * @brief 清空服务器的数据（包括清空本地db），慎用(重新初始化时会用到)
    * @param moduleName 模块名称，默认传空代表清空当前模块的数据
    */
    virtual bool clear(std::string_view moduleName = "") = 0;

    /**
     * @brief 初始化权限管理回调
     */
    virtual void initAuthorityCallback() = 0;
};
