#pragma once
#include "core/common/WDUtils.h"
#include "core/WDTranslate.h"

/**
 * @brief 指定Qt控件对该控件的显示文本进行翻译
 * @tparam T 控件类型
 * @param contextStr 翻译上下文
 * @param pControl 控件对象指针
*/
template <typename T>
void  Tr(const char* contextStr, T* pControl);
/**
 * @brief 指定Qt控件对该控件的显示文本进行翻译
 *  接口内部使用 WD::WDCxtTs(...), 因此调用该接口时，应该保证已经切换了正确的上下文对象
 *  eg:
 *      WD::WDCxtTsBg(myContextStr);
 *      Tr(ui.myWidget)
 *      Tr(ui.myLabel)
 *      Tr(ui.myPushButton)
 *      ...
 *      WD::WDCxtTsEd();
 * @tparam T 控件类型
 * @param pControl 控件对象指针
*/
template <typename T>
void  Tr(T* pControl);
/**
 * @brief 指定控件列表将列表中的所有控件进行翻译
 * @tparam ...Ts 控件类型列表
 * @param contextStr 翻译上下文
 * @param ...pControls 控件对象指针列表
*/
template <typename ...Ts>
void  Trs(const char* contextStr, Ts* ...pControls);

#include "UiTranslate.inl"
