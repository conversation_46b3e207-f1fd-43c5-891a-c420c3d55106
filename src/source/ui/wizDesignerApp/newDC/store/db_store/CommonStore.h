//
// Created by everpan on 25-5-13.
//

#ifndef CODE_PAIR_STORE_H
#define CODE_PAIR_STORE_H

#include <string>
#include <map>
#include "NodeMakeStorage.h"

namespace WD::store
{
    /**
     * 用于管理与远程同步的code pair
     * 所有项目共有
     * 进程启动即加载
     */
    class CommonStore
    {
    public:
        enum CodePairType
        {
            Attrs = 0,
            Type = 1
        };

        explicit CommonStore(const std::string& pathStr);
        /**
         * 加载数据
         * @param type 类型
         * @return 键值对数据
         */
        std::map<std::string, int64_t> load(CodePairType type) const;
        /**
         * 保存数据
         * @param data 键值对数据
         * @param type 类型
         */
        void save(const std::map<std::string, int64_t>& data, CodePairType type) const;

    private:
        CodePairStorageUPtr _storages[2];
    };

    using CommonStoreSPtr = std::shared_ptr<CommonStore>;
}


#endif //CODE_PAIR_STORE_H
