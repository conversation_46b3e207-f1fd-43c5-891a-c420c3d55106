//
// Created by everpan on 25-4-27.
//

#include "DbStore.h"
#include "NodeStorage.h"
#include <filesystem>
#include <memory>
#include <string>
#include <vector>
#include <iostream>
#include <shared_mutex>
#include <stdexcept>
#include <glog/logging.h>

namespace fs = std::filesystem;

namespace WD::store
{
    class DbStore::Impl
    {
        mutable std::shared_mutex _mtx;

    public:
        explicit Impl(const std::string& path, const std::string& prefix, int tableSize)
        {
            _nodeStorages.resize(NodeType::NodeTypeSize);
            _nodeStorages[NodeType::DESIGN] = std::make_unique<NodeStorage>(path, "design", prefix, tableSize);
            _nodeStorages[NodeType::CATALOG] = std::make_unique<NodeStorage>(path, "catalog", prefix, tableSize);
        }

        bool upsetNodeAttrsRecord(const design::NodeAttrsRecord& record, const std::string& uuid, NodeType type,
                                  StoreFrom from) const
        {
            try
            {
                std::unique_lock<std::shared_mutex> lock(_mtx);
                auto& storage = _nodeStorages[type]->getNodeAttrStorage(record.id());
                NodeAttrsRow attrs;
                attrs.fromNodeAttrsRecord(record, uuid);
                attrs.user = record.additionalinfo().user();
                attrs.status = 1;
                attrs.from = from;
                attrs.update_time = record.additionalinfo().time();
                storage.replace(attrs);
                return true;
            }
            catch (const std::exception& e)
            {
                LOG(ERROR) << "Error upserting node: " << e.what();
                return false;
            }
        }

        size_t batchUpsetNodeAttrsRecords(
            const std::vector<std::pair<design::NodeAttrsRecord, std::string>>& recordsWithUuids,
            NodeType type,
            StoreFrom from) const
        {
            if (recordsWithUuids.empty())
            {
                LOG(WARNING) << "Batch upset node attrs called with empty records vector";
                return 0;
            }

            size_t successCount = 0;

            try
            {
                // 按分片组织数据，提高批量插入效率
                std::map<int, std::vector<NodeAttrsRow>> shardedData;

                // 第一阶段：数据转换和分片分组
                for (const auto& [record, uuid] : recordsWithUuids)
                {
                    try
                    {
                        NodeAttrsRow attrs;
                        attrs.fromNodeAttrsRecord(record, uuid);
                        attrs.user = record.additionalinfo().user();
                        attrs.status = 1;
                        attrs.from = from;
                        attrs.update_time = record.additionalinfo().time();

                        // 计算分片索引
                        int shardIndex = record.id() % _nodeStorages[type]->getTableCount();
                        shardedData[shardIndex].push_back(std::move(attrs));
                    }
                    catch (const std::exception& e)
                    {
                        LOG(ERROR) << "Error converting node attrs record (id: " << record.id() << "): " << e.what();
                        // 继续处理其他记录
                    }
                }

                // 第二阶段：按分片批量插入
                for (const auto& [shardIndex, attrsRows] : shardedData)
                {
                    try
                    {
                        // 获取对应分片的存储
                        auto& storage = _nodeStorages[type]->getNodeAttrStorage(shardIndex);

                        // 使用锁保护当前分片的操作
                        std::unique_lock<std::shared_mutex> lock(_mtx);

                        // 为当前分片开始事务
                        storage.transaction([&]() {
                            for (const auto& attrs : attrsRows)
                            {
                                try
                                {
                                    storage.replace(attrs);
                                    successCount++;
                                }
                                catch (const std::exception& e)
                                {
                                    LOG(ERROR) << "Error inserting node attrs record (id: " << attrs.id
                                              << ", shard: " << shardIndex << "): " << e.what();
                                    // 继续处理其他记录
                                }
                            }
                            return true; // 提交事务
                        });

                        LOG(INFO) << "Shard " << shardIndex << " batch insert completed. "
                                 << attrsRows.size() << " records processed";
                    }
                    catch (const std::exception& e)
                    {
                        LOG(ERROR) << "Error in shard " << shardIndex << " batch insert: " << e.what();
                        // 继续处理其他分片
                    }
                }

                LOG(INFO) << "Batch upset node attrs records completed. Success: " << successCount
                         << "/" << recordsWithUuids.size() << " records";
            }
            catch (const std::exception& e)
            {
                LOG(ERROR) << "Error in batch upset node attrs records: " << e.what();
            }

            return successCount;
        }


        std::pair<size_t, size_t> batchUpsetNodeAttrsRecordsWithOptions(
            const std::vector<std::pair<design::NodeAttrsRecord, std::string>>& recordsWithUuids,
            NodeType type,
            StoreFrom from,
            size_t batchSize,
            bool stopOnError) const
        {
            if (recordsWithUuids.empty())
            {
                LOG(WARNING) << "Batch upset node attrs with options called with empty records vector";
                return {0, 0};
            }

            size_t successCount = 0;
            size_t processedCount = 0;

            // 如果 batchSize 为 0，则一次处理所有记录
            const size_t effectiveBatchSize = (batchSize == 0) ? recordsWithUuids.size() : batchSize;

            try
            {
                // 分批处理记录
                for (size_t startIdx = 0; startIdx < recordsWithUuids.size(); startIdx += effectiveBatchSize)
                {
                    const size_t endIdx = std::min(startIdx + effectiveBatchSize, recordsWithUuids.size());
                    const size_t currentBatchSize = endIdx - startIdx;

                    LOG(INFO) << "Processing node attrs batch " << (startIdx / effectiveBatchSize + 1)
                             << ", records " << startIdx << "-" << (endIdx - 1)
                             << " (size: " << currentBatchSize << ")";

                    try
                    {
                        // 为当前批次按分片组织数据
                        std::map<int, std::vector<NodeAttrsRow>> shardedBatchData;

                        // 转换当前批次的数据
                        for (size_t i = startIdx; i < endIdx; ++i)
                        {
                            try
                            {
                                const auto& [record, uuid] = recordsWithUuids[i];

                                NodeAttrsRow attrs;
                                attrs.fromNodeAttrsRecord(record, uuid);
                                attrs.user = record.additionalinfo().user();
                                attrs.status = 1;
                                attrs.from = from;
                                attrs.update_time = record.additionalinfo().time();

                                // 计算分片索引
                                int shardIndex = record.id() % _nodeStorages[type]->getTableCount();
                                shardedBatchData[shardIndex].push_back(std::move(attrs));
                                processedCount++;
                            }
                            catch (const std::exception& e)
                            {
                                LOG(ERROR) << "Error converting node attrs record (index: " << i << "): " << e.what();
                                processedCount++;

                                if (stopOnError)
                                {
                                    LOG(WARNING) << "Stopping batch processing due to conversion error (stopOnError=true)";
                                    return {successCount, processedCount};
                                }
                            }
                        }

                        // 按分片插入当前批次的数据
                        for (const auto& [shardIndex, attrsRows] : shardedBatchData)
                        {
                            try
                            {
                                auto& storage = _nodeStorages[type]->getNodeAttrStorage(shardIndex);
                                std::unique_lock<std::shared_mutex> lock(_mtx);

                                storage.transaction([&]() {
                                    size_t shardSuccessCount = 0;

                                    for (const auto& attrs : attrsRows)
                                    {
                                        try
                                        {
                                            storage.replace(attrs);
                                            shardSuccessCount++;
                                        }
                                        catch (const std::exception& e)
                                        {
                                            LOG(ERROR) << "Error inserting node attrs record (id: " << attrs.id
                                                      << ", shard: " << shardIndex << "): " << e.what();

                                            if (stopOnError)
                                            {
                                                LOG(WARNING) << "Stopping shard processing due to insert error";
                                                throw; // 重新抛出异常以停止处理
                                            }
                                        }
                                    }

                                    successCount += shardSuccessCount;
                                    return true; // 提交事务
                                });
                            }
                            catch (const std::exception& e)
                            {
                                LOG(ERROR) << "Error in shard " << shardIndex << " batch processing: " << e.what();

                                if (stopOnError)
                                {
                                    LOG(WARNING) << "Stopping all batch processing due to shard error";
                                    return {successCount, processedCount};
                                }
                            }
                        }

                        LOG(INFO) << "Node attrs batch " << (startIdx / effectiveBatchSize + 1) << " completed";
                    }
                    catch (const std::exception& e)
                    {
                        LOG(ERROR) << "Error in node attrs batch " << (startIdx / effectiveBatchSize + 1) << ": " << e.what();

                        if (stopOnError)
                        {
                            LOG(WARNING) << "Stopping all batch processing due to batch error";
                            break;
                        }
                    }
                }

                LOG(INFO) << "Batch upset node attrs records with options completed. "
                         << "Total success: " << successCount << "/" << processedCount
                         << " (out of " << recordsWithUuids.size() << " total records)";
            }
            catch (const std::exception& e)
            {
                LOG(ERROR) << "Fatal error in batch upset node attrs records with options: " << e.what();
            }

            return {successCount, processedCount};
        }

        bool deleteNode(int64_t id, NodeType type) const
        {
            try
            {
                std::unique_lock<std::shared_mutex> lock(_mtx);
                auto& storage = _nodeStorages[type]->getNodeAttrStorage(id);
                storage.remove<NodeAttrsRow>(id);
                return true;
            }
            catch (const std::exception& e)
            {
                LOG(ERROR) << "Error deleting node: " << e.what();
                return false;
            }
        }

        design::NodeAttrsRecord getNodeAttrsRecord(int64_t id, const NodeType type) const
        {
            auto& storage = _nodeStorages[type]->getNodeAttrStorage(id);
            std::unique_lock<std::shared_mutex> lock(_mtx);
            auto attrs = storage.get_optional<NodeAttrsRow>(id);
            if (!attrs)
            {
                throw std::runtime_error("Node not found");
            }
            design::NodeAttrsRecord record;
            attrs->toNodeAttrsRecord(record);
            return record;
        }

        bool upsetNodeTreeRecord(const design::NodeTreeRecord& record, NodeType type, StoreFrom from) const
        {
            auto& storage = _nodeStorages[type]->getTreeStorage();
            try
            {
                NodeTreeRow r2;
                r2.fromNodeTreeRecord(record);
                r2.from = from;
                std::unique_lock<std::shared_mutex> lock(_mtx);
                storage.replace(r2);
                return true;
            }
            catch (const std::exception& e)
            {
                LOG(ERROR) << "Error updating tree: " << e.what();
                return false;
            }
        }

        size_t batchUpsetNodeTreeRecords(const std::vector<design::NodeTreeRecord>& records, NodeType type, StoreFrom from) const
        {
            if (records.empty())
            {
                LOG(WARNING) << "Batch upset called with empty records vector";
                return 0;
            }

            auto& storage = _nodeStorages[type]->getTreeStorage();
            size_t successCount = 0;

            try
            {
                // 使用单个锁来保护整个批量操作，提高性能
                std::unique_lock<std::shared_mutex> lock(_mtx);

                // 开始事务以提高批量插入性能
                auto transaction = storage.transaction([&]() {
                    // 预先准备所有要插入的数据
                    std::vector<NodeTreeRow> treeRows;
                    treeRows.reserve(records.size());

                    // 第一阶段：数据转换和验证
                    for (const auto& record : records)
                    {
                        try
                        {
                            NodeTreeRow r2;
                            r2.fromNodeTreeRecord(record);
                            r2.from = from;
                            treeRows.push_back(std::move(r2));
                        }
                        catch (const std::exception& e)
                        {
                            LOG(ERROR) << "Error converting tree record (id: " << record.id() << "): " << e.what();
                            // 继续处理其他记录
                        }
                    }

                    // 第二阶段：批量插入数据
                    for (const auto& treeRow : treeRows)
                    {
                        try
                        {
                            storage.replace(treeRow);
                            successCount++;
                        }
                        catch (const std::exception& e)
                        {
                            LOG(ERROR) << "Error inserting tree record (id: " << treeRow.id << "): " << e.what();
                            // 在事务中，如果有错误可以选择回滚或继续
                            // 这里选择继续处理其他记录
                        }
                    }

                    return true; // 提交事务
                });

                LOG(INFO) << "Batch upset tree records completed. Success: " << successCount
                         << "/" << records.size() << " records";
            }
            catch (const std::exception& e)
            {
                LOG(ERROR) << "Error in batch upset tree records: " << e.what();
                // 事务会自动回滚
            }

            return successCount;
        }

        std::pair<size_t, size_t> batchUpsetNodeTreeRecordsWithOptions(
            const std::vector<design::NodeTreeRecord>& records,
            NodeType type,
            StoreFrom from,
            size_t batchSize,
            bool stopOnError) const
        {
            if (records.empty())
            {
                LOG(WARNING) << "Batch upset called with empty records vector";
                return {0, 0};
            }

            auto& storage = _nodeStorages[type]->getTreeStorage();
            size_t successCount = 0;
            size_t processedCount = 0;

            // 如果 batchSize 为 0，则一次处理所有记录
            const size_t effectiveBatchSize = (batchSize == 0) ? records.size() : batchSize;

            try
            {
                // 分批处理记录
                for (size_t startIdx = 0; startIdx < records.size(); startIdx += effectiveBatchSize)
                {
                    const size_t endIdx = std::min(startIdx + effectiveBatchSize, records.size());
                    const size_t currentBatchSize = endIdx - startIdx;

                    LOG(INFO) << "Processing batch " << (startIdx / effectiveBatchSize + 1)
                             << ", records " << startIdx << "-" << (endIdx - 1)
                             << " (size: " << currentBatchSize << ")";

                    try
                    {
                        // 使用锁保护当前批次
                        std::unique_lock<std::shared_mutex> lock(_mtx);

                        // 为当前批次开始事务
                        storage.transaction([&]() {
                            size_t batchSuccessCount = 0;

                            for (size_t i = startIdx; i < endIdx; ++i)
                            {
                                try
                                {
                                    NodeTreeRow r2;
                                    r2.fromNodeTreeRecord(records[i]);
                                    r2.from = from;

                                    storage.replace(r2);
                                    batchSuccessCount++;
                                    processedCount++;
                                }
                                catch (const std::exception& e)
                                {
                                    LOG(ERROR) << "Error processing tree record (id: " << records[i].id()
                                              << ", index: " << i << "): " << e.what();
                                    processedCount++;

                                    if (stopOnError)
                                    {
                                        LOG(WARNING) << "Stopping batch processing due to error (stopOnError=true)";
                                        throw; // 重新抛出异常以停止处理
                                    }
                                }
                            }

                            successCount += batchSuccessCount;
                            return true; // 提交当前批次的事务
                        });

                        LOG(INFO) << "Batch " << (startIdx / effectiveBatchSize + 1) << " completed. "
                                 << "Success: " << (successCount - (successCount - currentBatchSize))
                                 << "/" << currentBatchSize << " records";
                    }
                    catch (const std::exception& e)
                    {
                        LOG(ERROR) << "Error in batch " << (startIdx / effectiveBatchSize + 1) << ": " << e.what();

                        if (stopOnError)
                        {
                            LOG(WARNING) << "Stopping all batch processing due to error";
                            break;
                        }
                        // 继续处理下一个批次
                    }
                }

                LOG(INFO) << "Batch upset tree records with options completed. "
                         << "Total success: " << successCount << "/" << processedCount
                         << " (out of " << records.size() << " total records)";
            }
            catch (const std::exception& e)
            {
                LOG(ERROR) << "Fatal error in batch upset tree records with options: " << e.what();
            }

            return {successCount, processedCount};
        }

        bool deleteTree(int64_t parent, NodeType type) const
        {
            try
            {
                auto& storage = _nodeStorages[type]->getTreeStorage();
                std::unique_lock<std::shared_mutex> lock(_mtx);
                storage.remove<NodeTreeRow>(parent);
                return true;
            }
            catch (const std::exception& e)
            {
                LOG(ERROR) << "Error deleting tree: " << e.what();
                return false;
            }
        }

        design::NodeTreeRecord getNodeTreeRecord(int64_t parent, NodeType type) const
        {
            design::NodeTreeRecord r2;
            try
            {
                auto& storage = _nodeStorages[type]->getTreeStorage();
                std::shared_lock<std::shared_mutex> lock(_mtx);
                auto record = storage.get_optional<NodeTreeRow>(parent);
                if (record)
                {
                    record->toNodeTreeRecord(r2);
                }
            }
            catch (const std::exception& e)
            {
                LOG(ERROR) << "Error getting tree: " << e.what();
            }
            return r2;
        }

        std::vector<int64_t> getNodeChildIds(int64_t parent, NodeType type)
        {
            auto& storage = _nodeStorages[type]->getTreeStorage();
            std::shared_lock<std::shared_mutex> lock(_mtx);
            auto record = storage.get_optional<NodeTreeRow>(parent);
            if (record)
            {
                lock.unlock();
                rapidjson::Document doc;
                doc.Parse(record->children.c_str());
                std::vector<int64_t> children;
                for (const auto& child : doc.GetArray())
                {
                    children.push_back(child.GetInt64());
                }
                return children;
            }
            return {};
        }

        int64_t fetchMaxUpdateTime(NodeType type) const
        {
            auto& storage = _nodeStorages[type];
            return storage->fetchMaxUpdateTime();
        }

        int64_t fetchTraceId(int64_t id, NodeType type, bool isTree)
        {
            try
            {
                std::shared_lock<std::shared_mutex> lock(_mtx);
                if (isTree)
                {
                    auto& storage = _nodeStorages[type]->getTreeStorage();
                    auto record = storage.get_optional<NodeTreeRow>(id);
                    if (record)
                    {
                        return record->trace_id;
                    }
                }
                else
                {
                    auto& storage = _nodeStorages[type]->getNodeAttrStorage(id);
                    auto record = storage.get_optional<NodeAttrsRow>(id);
                    if (record)
                    {
                        return record->trace_id;
                    }
                }
            }
            catch (const std::exception& e)
            {
                LOG(ERROR) << "Error fetching trace id: " << e.what();
            }
            return 0;
        }

        bool updateNodeCheckOutStatus(int64_t id, NodeType type, int check_out_status, const std::string& user)
        {
            auto& storage = _nodeStorages[type]->getNodeAttrStorage(id);
            try
            {
                std::unique_lock<std::shared_mutex> lock(_mtx);
                storage.update_all(sqlite_orm::set(sqlite_orm::c(&NodeAttrsRow::check_out) = check_out_status,
                                   sqlite_orm::c(&NodeAttrsRow::check_out_user) = user),
                                   sqlite_orm::where(&NodeAttrsRow::id == id));
            }
            catch (const std::exception& e)
            {
                LOG(ERROR) << "Error updating node check out status: " << e.what();
            }
            return false;
        }

    private:
        std::vector<std::unique_ptr<NodeStorage>> _nodeStorages;
    };

    DbStore::DbStore(const std::string& path, const std::string& prefix, int tableSize)
        : pImpl(std::make_unique<Impl>(path, prefix, tableSize))
    {
    }

    DbStore::~DbStore() = default;

    bool DbStore::upsetNodeAttrsRecord(const design::NodeAttrsRecord& record, const std::string& uuid, NodeType type,
                                       StoreFrom from)
    {
        return pImpl->upsetNodeAttrsRecord(record, uuid, type, from);
    }

    size_t DbStore::batchUpsetNodeAttrsRecords(
        const std::vector<std::pair<design::NodeAttrsRecord, std::string>>& recordsWithUuids,
        NodeType type,
        StoreFrom from)
    {
        return pImpl->batchUpsetNodeAttrsRecords(recordsWithUuids, type, from);
    }

    std::pair<size_t, size_t> DbStore::batchUpsetNodeAttrsRecordsWithOptions(
        const std::vector<std::pair<design::NodeAttrsRecord, std::string>>& recordsWithUuids,
        NodeType type,
        StoreFrom from,
        size_t batchSize,
        bool stopOnError)
    {
        return pImpl->batchUpsetNodeAttrsRecordsWithOptions(recordsWithUuids, type, from, batchSize, stopOnError);
    }

    bool DbStore::deleteNode(int64_t id, NodeType type)
    {
        return pImpl->deleteNode(id, type);
    }

    design::NodeAttrsRecord DbStore::getNodeAttrsRecord(int64_t id, NodeType type)
    {
        return pImpl->getNodeAttrsRecord(id, type);
    }

    bool DbStore::upsetNodeTreeRecord(const design::NodeTreeRecord& record, NodeType type, StoreFrom from)
    {
        return pImpl->upsetNodeTreeRecord(record, type, from);
    }

    size_t DbStore::batchUpsetNodeTreeRecords(const std::vector<design::NodeTreeRecord>& records, NodeType type, StoreFrom from)
    {
        return pImpl->batchUpsetNodeTreeRecords(records, type, from);
    }

    std::pair<size_t, size_t> DbStore::batchUpsetNodeTreeRecordsWithOptions(
        const std::vector<design::NodeTreeRecord>& records,
        NodeType type,
        StoreFrom from,
        size_t batchSize,
        bool stopOnError)
    {
        return pImpl->batchUpsetNodeTreeRecordsWithOptions(records, type, from, batchSize, stopOnError);
    }

    bool DbStore::deleteTree(int64_t parent, NodeType type)
    {
        return pImpl->deleteTree(parent, type);
    }

    design::NodeTreeRecord DbStore::getNodeTreeRecord(int64_t parent, NodeType type)
    {
        return pImpl->getNodeTreeRecord(parent, type);
    }

    std::vector<int64_t> DbStore::getNodeChildIds(int64_t parent, NodeType type)
    {
        return pImpl->getNodeChildIds(parent, type);
    }

    bool DbStore::updateNodeCheckOutStatus(int64_t id, NodeType type, int checkOutStatus, const std::string& user)
    {
        return pImpl->updateNodeCheckOutStatus(id, type, checkOutStatus, user);
    }

    int64_t DbStore::fetchMaxUpdateTime(NodeType type)
    {
        return pImpl->fetchMaxUpdateTime(type);
    }

    int64_t DbStore::fetchTraceId(int64_t id, NodeType type, bool isTree)
    {
        return pImpl->fetchTraceId(id, type, isTree);
    }
}
