//
// Created by everpan on 25-4-27.
//

#ifndef NODE_MAKE_STORAGE_H
#define NODE_MAKE_STORAGE_H
#include "sqlite_orm.h"
#include "document.h"
#include "writer.h"
#include <string>
#include <memory>
#include <vector>

#include "NodeMakeStorage.h"
#include "proto/def.h"

namespace WD::store
{
    /**
     * @brief Database table structure for node attribute records
     * Used to store data from design::NodeAttrsRecord
     * This structure is used for the sharded tables based on node ID
     */
    struct NodeAttrsRow
    {
        int64_t id; // Node ID
        std::string uuid;
        std::string name; // Node name
        int32_t type; // Node type
        std::vector<char> attrs; // Node attributes (serialized as protobuf)
        std::string user; // User
        int64_t update_time; // Server update time
        int32_t check_out; // Check-out status
        int32_t status; // Status
        int64_t trace_id; // Trace ID
        int32_t from; // IStore::from
        int32_t partition;
        int64_t offset;
        std::string check_out_user;
        std::string additional_json; // Additional information (JSON format)
        void fromNodeAttrsRecord(const design::NodeAttrsRecord& record, const std::string& uuid = "");
        bool toNodeAttrsRecord(design::NodeAttrsRecord& record) const;
    };

    inline void NodeAttrsRow::fromNodeAttrsRecord(const design::NodeAttrsRecord& record, const std::string& uuid_)
    {
        id = record.id();
        uuid = uuid_;
        if (uuid.empty())
        {
            // todo form coder
        }
        name = record.name();
        type = record.type();
        auto len = record.ByteSizeLong();
        attrs.resize(len); // Resize instead of reserve to allocate actual memory
        record.SerializeToArray(attrs.data(), static_cast<int>(len));
        user = record.additionalinfo().user();
        update_time = record.additionalinfo().time();
        check_out = record.additionalinfo().checkout();
        check_out_user = record.additionalinfo().checkoutuser();
        status = record.additionalinfo().status();
        trace_id = record.traceid();
        additional_json = record.additionaljson();
    }

    inline bool NodeAttrsRow::toNodeAttrsRecord(design::NodeAttrsRecord& record) const
    {
        return record.ParseFromArray(attrs.data(), static_cast<int>(attrs.size()));
    }

    struct NodeTreeRow
    {
        int64_t id; // Node ID
        std::string children; // Child node list (serialized as JSON)
        std::string user; // User
        int64_t update_time; // Update time
        int32_t check_out; // Check-out status
        int32_t status; // Status
        int64_t trace_id; // Trace ID
        int32_t from; // IStore::from
        std::string additional_json; // Additional information (JSON format)
        void fromNodeTreeRecord(const design::NodeTreeRecord& record);
        bool toNodeTreeRecord(design::NodeTreeRecord& record) const;
    };

    inline void NodeTreeRow::fromNodeTreeRecord(const design::NodeTreeRecord& record)
    {
        id = record.id();
        // JSON to children
        rapidjson::Document doc;
        auto& alloc = doc.GetAllocator();
        rapidjson::Value arr(rapidjson::kArrayType);
        for (const auto& child : record.children().vec_int64())
        {
            arr.PushBack(child, alloc);
        }
        rapidjson::StringBuffer buffer;
        rapidjson::Writer<rapidjson::StringBuffer> writer(buffer);
        arr.Accept(writer);
        children = buffer.GetString();

        user = record.additionalinfo().user();
        update_time = record.additionalinfo().time();
        check_out = record.additionalinfo().checkout();
        status = record.additionalinfo().status();
        trace_id = record.traceid();
        additional_json = record.additionaljson();
    }

    inline bool NodeTreeRow::toNodeTreeRecord(design::NodeTreeRecord& record) const
    {
        record.set_id(id);
        // from JSON
        const auto children1 = record.mutable_children();
        rapidjson::Document doc;
        doc.Parse(children.c_str());
        for (const auto& child : doc.GetArray())
        {
            children1->add_vec_int64(child.GetInt64());
        }

        const auto additionalInfo = record.mutable_additionalinfo();
        additionalInfo->set_user(user);
        additionalInfo->set_time(update_time);
        additionalInfo->set_checkout(check_out);
        additionalInfo->set_status(status);
        record.set_traceid(trace_id);
        record.set_additionaljson(additional_json);
        return true;
    }

    inline auto makeNodeAttrStorage(const std::string& dbFile, const std::string& tableId)
    {
        using namespace sqlite_orm;
        return make_storage(dbFile,
                            // index
                            make_index("idx_type", &NodeAttrsRow::type),
                            make_index("idx_uuid", &NodeAttrsRow::uuid),
                            make_index("idx_user", &NodeAttrsRow::user),
                            make_index("idx_update_time", &NodeAttrsRow::update_time),
                            make_index("idx_from", &NodeAttrsRow::from),
                            make_table("node_attrs_" + tableId,
                                       make_column("id", &NodeAttrsRow::id, primary_key()),
                                       make_column("uuid", &NodeAttrsRow::uuid),
                                       make_column("name", &NodeAttrsRow::name),
                                       make_column("type", &NodeAttrsRow::type),
                                       make_column("attrs", &NodeAttrsRow::attrs), // blob
                                       make_column("user", &NodeAttrsRow::user),
                                       make_column("update_time", &NodeAttrsRow::update_time),
                                       make_column("check_out", &NodeAttrsRow::check_out),
                                       make_column("status", &NodeAttrsRow::status),
                                       make_column("trace_id", &NodeAttrsRow::trace_id),
                                       make_column("from", &NodeAttrsRow::from),
                                       make_column("additional_json", &NodeAttrsRow::additional_json)
                            ));
    }

    inline auto makeNodeTreeStorage(const std::string& dbFile)
    {
        using namespace sqlite_orm;
        return make_storage(dbFile,
                            // index
                            make_index("idx_user", &NodeTreeRow::user),
                            make_index("idx_from", &NodeTreeRow::from),
                            make_table("node_tree",
                                       make_column("id", &NodeTreeRow::id, primary_key()),
                                       make_column("children", &NodeTreeRow::children),
                                       make_column("user", &NodeTreeRow::user),
                                       make_column("update_time", &NodeTreeRow::update_time),
                                       make_column("check_out", &NodeTreeRow::check_out),
                                       make_column("status", &NodeTreeRow::status),
                                       make_column("trace_id", &NodeTreeRow::trace_id),
                                       make_column("from", &NodeTreeRow::from),
                                       make_column("additional_json", &NodeTreeRow::additional_json)
                            ));
    }

    // 记录本地项目的一些信息； 例如 最后更新时间，避免遍历所有的数据
    struct ProjectInfoRow
    {
        uint64_t project_id;
        std::string project_code;
        int64_t last_update_time; // 本地最后的更新时间
    };

    inline auto makeProjectInfoStorage(const std::string& dbFile)
    {
        using namespace sqlite_orm;
        return make_storage(dbFile,
                            make_table("project_info",
                                       make_column("project_id", &ProjectInfoRow::project_id, primary_key()),
                                       make_column("project_code", &ProjectInfoRow::project_code),
                                       make_column("last_update_time", &ProjectInfoRow::last_update_time)
                            ));
    }


    struct CodePairRow
    {
        std::string key;
        int64_t code;
    };

    inline auto makeCodePairStorage(const std::string& dbFile, const std::string& tableSubfix)
    {
        using namespace sqlite_orm;
        const std::string table = "code_pair_" + tableSubfix;
        return make_storage(dbFile,
                            make_index("idx_from", &CodePairRow::key),
                            make_table(table,
                                       make_column("key", &CodePairRow::key, primary_key()),
                                       make_column("code", &CodePairRow::code)
                            ));
    }

    struct NodeTreeActionRow
    {
        int64_t id;
        int64_t parent_id;
        int32_t flag;
        int64_t left_sibling_id;
        std::string siblings;
        void fromNodeTreeAction(const design::NodeTreeAction& action);
        void toNodeTreeAction(design::NodeTreeAction& action) const;
    };

    inline void NodeTreeActionRow::fromNodeTreeAction(const design::NodeTreeAction& action)
    {
        parent_id = action.parentid();
        flag = action.flag();
        left_sibling_id = action.leftsiblingid();
        rapidjson::Document doc;
        auto& alloc = doc.GetAllocator();
        rapidjson::Value arr(rapidjson::kArrayType);
        for (const auto& child : action.siblings())
        {
            arr.PushBack(child, alloc);
        }
        rapidjson::StringBuffer buffer;
        rapidjson::Writer<rapidjson::StringBuffer> writer(buffer);
        arr.Accept(writer);
        siblings = buffer.GetString();
    }

    inline void NodeTreeActionRow::toNodeTreeAction(design::NodeTreeAction& action) const
    {
        action.set_parentid(parent_id);
        action.set_flag(static_cast<design::TreeActionFlag>(flag));
        action.set_leftsiblingid(left_sibling_id);
        rapidjson::Document doc;
        doc.Parse(siblings.c_str());
        for (const auto& child : doc.GetArray())
        {
            action.add_siblings(child.GetInt64());
        }
    }

    inline auto makeNodeTreeActionStorage(const std::string& dbFile)
    {
        using namespace sqlite_orm;
        return make_storage(dbFile,
                            make_table("node_tree_action",
                                       make_column("id", &NodeTreeActionRow::id, primary_key().autoincrement()),
                                       make_column("parent_id", &NodeTreeActionRow::parent_id),
                                       make_column("flag", &NodeTreeActionRow::flag),
                                       make_column("left_sibling_id", &NodeTreeActionRow::left_sibling_id),
                                       make_column("siblings", &NodeTreeActionRow::siblings)
                            ));
    }

    using NodeAttrStorage = decltype(makeNodeAttrStorage("", ""));
    using NodeAttrStorageUPtr = std::unique_ptr<NodeAttrStorage>;
    using NodeTreeStorage = decltype(makeNodeTreeStorage(""));
    using NodeTreeStorageUPtr = std::unique_ptr<NodeTreeStorage>;
    using ProjectInfoStorage = decltype(makeProjectInfoStorage(""));
    using ProjectInfoStorageUPtr = std::unique_ptr<ProjectInfoStorage>;
    using CodePairStorage = decltype(makeCodePairStorage("", ""));
    using CodePairStorageUPtr = std::unique_ptr<CodePairStorage>;
}

#endif //NODE_MAKE_STORAGE_H
