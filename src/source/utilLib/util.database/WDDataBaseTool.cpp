#include "WDDataBaseTool.h"
#include "common/WDMD5.h"
#include <filesystem>
#include "businessModule/catalog/WDBMCatalogMgr.h"
#include "businessModule/design/WDBMDesignMgr.h"

WD_NAMESPACE_BEGIN

WDDataBaseTool::WDDataBaseTool(WDCore& core, bool isCatalog)
    : _core(core)
    , _isCatalogModule(isCatalog)
{
};

WDDataBaseTool::~WDDataBaseTool()
{
};


WDNodeSPtr  WDDataBaseTool::fetchNode(const char* nodeId)
{
    return fetchNode(WDUuid::FromString(nodeId));
}

WDNodeSPtr  WDDataBaseTool::fetchNode(const WDUuid& nodeUuid)
{
    if (!isValid())
        return nullptr;

    WDNodeSPtr pNode = WDNode::MakeShared();
    WDStmtSPtr stmt = nullptr;

    // ȷ�ϸýڵ����
    if (!nodeExist(nodeUuid))
        return nullptr;

    // ��������id
    WDUuid propertyUuid;
    ArrayUuid propertyUuids;
    stmt = getStmt(SqlSelectNodePtyId());
    if (!stmt)
        return nullptr;
    stmt->bindParam(0, &nodeUuid, _SizeOfUuid, WD_TYPE_BLOB);
    stmt->bindResult(0, &propertyUuid, _SizeOfUuid, WD_TYPE_BLOB);
    stmt->store();

    while (stmt->fetch())
    {
        propertyUuids.push_back(propertyUuid);
    }
    for (const auto& pptUid : propertyUuids)
    {
        // ��������
        WDPropertyGroup tempGrp;
        fetchProperty(pptUid, tempGrp);
        if (tempGrp.propertys().empty())
            return false;
        auto pGrp = WDProperty::As<WDPropertyGroup>(tempGrp.propertys().front().get());
        if (pGrp != nullptr)
            pNode->fromSerialProperties(*pGrp);
    }
    // �������id
    WDUuid componentId;
    ArrayUuid componentIds;
    stmt = getStmt(SqlSelectNodeCpnId());
    if(stmt)
    {
        stmt->bindParam(0, &nodeUuid, _SizeOfUuid, WD_TYPE_BLOB);
        stmt->bindResult(0, &componentId, _SizeOfUuid, WD_TYPE_BLOB);
        stmt->store();
        while (stmt->fetch())
        {
            componentIds.push_back(componentId);
        }
        for(const auto& cpnUid: componentIds)
        {
            auto pCom = fetchComponent(cpnUid);
            pNode->addComponent(pCom);
        }
    }


    // �����ӽڵ�
    WDUuid childUuid;
    ArrayUuid childUuids;
    stmt = getStmt(SqlSelectNodeChildId());
    if (stmt)
    {
        stmt->bindParam(0, &nodeUuid, _SizeOfUuid, WD_TYPE_BLOB);
        stmt->bindResult(0, &childUuid, _SizeOfUuid, WD_TYPE_BLOB);
        stmt->store();
        while (stmt->fetch())
        {
            childUuids.push_back(childUuid);
        }
        for (const auto& cUid : childUuids)
        {
            // �ݹ��ӽڵ�
            WDNodeSPtr childNode = fetchNode(cUid);
            if(childNode != nullptr)
                childNode->setParent(pNode.get());
        }
    }
    return pNode;
}

size_t  WDDataBaseTool::fetchNodesByNodeUuid(Objects& objs, const Strings& nodeUuids)
{
    uint num = 0;
    for (const auto& nodeUid : nodeUuids)
    {
        WDNodeSPtr pNode = fetchNode(nodeUid.c_str());
        if(pNode == nullptr)
            continue;
        objs.push_back(pNode);
        num++;
    }
    return num;
}

size_t  WDDataBaseTool::fetchByIdToStream(const char* nodeUuid, Stream& pBuf)
{
    WDNodeSPtr pNode = fetchNode(nodeUuid); // ���ҳ��ڵ�
    if(pNode == nullptr)
        return 0;

    return WDWdTool::WriteNodesToWdStream({pNode}, pBuf);
}

size_t  WDDataBaseTool::fetchByNameToStream(const char* name, Stream& pBuf)
{
    Objects nodes;
    fetchNodesByNodeName(nodes, {name});
    if(nodes.empty())
        return 0;
    return WDWdTool::WriteNodesToWdStream(nodes, pBuf);
}

size_t  WDDataBaseTool::fetchByIdToPr(const char* nodeUuid, Stream& stream, Version prVersion)
{
    Objects objs;
    Nodes nodes;
    fetchNodesByNodeUuid(objs, {nodeUuid});
    if (objs.empty())
        return 0;
    MapNodes refNodes;
    bool loadRef = true;
    for (const auto& obj : objs) // objs.size() == 1
    {
        auto pNode = obj->toPtr<WDNode>();
        if (pNode == nullptr)
            continue;
        nodes.push_back(pNode);
        if (!loadRefNodeForBranch(*pNode, refNodes))
            loadRef = false;
        pNode->update(true);
    }

    if (!loadRef)
        assert(false && "���refNode��ȡʧ�ܣ�");

    WDPrTool prTool(_core);
    return prTool.writeToStream(nodes, stream, prVersion, nullptr);
}

size_t  WDDataBaseTool::fetchByNameToPr(const char* name, Stream& stream, Version prVersion)
{
    Objects objs;
    Nodes nodes;
    fetchNodesByNodeName(objs, {name});
    if (objs.empty())
        return 0;
    MapNodes refNodes;
    bool loadRef = true;
    for (const auto& obj : objs)
    {
        auto pNode = obj->toPtr<WDNode>();
        if (pNode == nullptr)
            continue;
        nodes.push_back(pNode);
        if (!loadRefNodeForBranch(*pNode, refNodes))
            loadRef = false;
        pNode->update(true);
    }
    if (!loadRef)
        assert(false && "���refNode��ȡʧ�ܣ�");
    WDPrTool prTool(_core);
    return prTool.writeToStream(nodes, stream, prVersion, nullptr);
}

void    WDDataBaseTool::fetchProperty(const WDUuid& propertyUuid, WDPropertyGroup& parent)
{
    //��ѯ����
    WDStmtSPtr stmt = getStmt(SqlSelectPtyInfo());
    if (!stmt)
        return ;
    stmt->bindParam(0, &propertyUuid, _SizeOfUuid, WD_TYPE_BLOB);
    // ���ڲ�֪���ַ������ݵ����ж೤����������Ķ�ȡ��ʽ������ͬ
    // ��Ҫ�ȶ�ȡ�������ȡ������ȣ��ٰ��㹻���ȵ�buffer��ȡ����
    stmt->bindResult(0, nullptr, 0, WD_TYPE_STRING);
    stmt->bindResult(1, nullptr, 0, WD_TYPE_STRING);
    stmt->bindResult(2, nullptr, 0, WD_TYPE_STRING);
    stmt->store();
    if(!stmt->fetch())
        return;
    std::string type, name, value;
    // ��ȡ����
    size_t typeSize     =   stmt->columnDataSize(0);
    size_t nameSize     =   stmt->columnDataSize(1);
    size_t valueSize    =   stmt->columnDataSize(2);
    if (typeSize)
    {
        type.resize(typeSize);
        stmt->fetchColumn(0, type.data(), typeSize, 0);
    }
    if (nameSize)
    {
        name.resize(nameSize);
        stmt->fetchColumn(1, name.data(), nameSize, 0);
    }
    if (valueSize)
    {
        value.resize(valueSize);
        stmt->fetchColumn(2, value.data(), valueSize, 0);
    }
    fetchProperty(propertyUuid, parent, type, name, value);
}

void    WDDataBaseTool::fetchProperty(const WDUuid& propertyUuid
                                    , WDPropertyGroup& parent
                                    , const std::string& type
                                    , const std::string& name
                                    , const std::string& value)
{
    WDStmtSPtr stmt = nullptr;

    WDPropertyDataType  propertyDataType    =   PropertyDataTypeFromName(type.c_str());
    auto pty    =   parent.addProperty(propertyDataType, name);
    pty->setName(name);
    // ����value����Ϊ�����ݵ����
    if (strcmp(_BigDataTag, value.c_str()) == 0)
    {
        // �������ݱ�
        std::string bigData;
        stmt = getStmt(SqlSelectBigData());
        if(!stmt)
            return ;
        stmt->bindParam(0, &propertyUuid, _SizeOfUuid, WD_TYPE_BLOB);
        stmt->bindResult(0, nullptr, 0, WD_TYPE_LONG_BLOB);
        stmt->store();
        if(!stmt->fetch())
            return;
        size_t bigDataSize = stmt->columnDataSize(0);
        bigData.resize(bigDataSize);
        stmt->fetchColumn(0, bigData.data(), bigDataSize, 0);
        pty->valueFromString(bigData.c_str());
    }
    else
        pty->valueFromString(value);

    auto pGrp   =   WDProperty::As<WD::WDPropertyGroup>(pty);
    if(pGrp == nullptr)
        return ;// ������������ﷵ��

    //����������s
    WDUuid childUuid;
    stmt = getStmt(SqlSelectPtyChildInfo());
    if(!stmt)
        return ;
    stmt->bindParam(0, &propertyUuid, _SizeOfUuid, WD_TYPE_BLOB);
    stmt->bindResult(0, &childUuid, _SizeOfUuid, WD_TYPE_BLOB);
    stmt->bindResult(1, nullptr, 0, WD_TYPE_STRING);
    stmt->bindResult(2, nullptr, 0, WD_TYPE_STRING);
    stmt->bindResult(3, nullptr, 0, WD_TYPE_STRING);
    stmt->store();
    std::string childType, childName, childValue;
    while (stmt->fetch())
    {
        // ��ȡ����
        size_t typeSize     =   stmt->columnDataSize(1);
        size_t nameSize     =   stmt->columnDataSize(2);
        size_t valueSize    =   stmt->columnDataSize(3);

        if(typeSize)
        {
            childType.resize(typeSize);
            stmt->fetchColumn(1, childType.data(), typeSize, 0);
        }
        if(nameSize)
        {
            childName.resize(nameSize);
            stmt->fetchColumn(2, childName.data(), nameSize, 0);
        }
        if(valueSize)
        {
            childValue.resize(valueSize);
            stmt->fetchColumn(3, childValue.data(), valueSize, 0);
        }
        fetchProperty(childUuid, *pGrp, childType, childName, childValue);
    }
}

WDNodeComponentSPtr WDDataBaseTool::fetchComponent(const WDUuid& componentUuid)
{
    WDStmtSPtr    stmt =   nullptr;
    WDNodeComponentSPtr         pCom    =   nullptr;
    WDPropertyGroup*            pGrp    =   nullptr;

    //��typeId
    WDUuid typeId;
    stmt = getStmt(SqlSelectCpnTypeId());
    if (!stmt)
        return nullptr;
    stmt->bindParam(0, &componentUuid, _SizeOfUuid, WD_TYPE_BLOB);
    stmt->bindResult(0, &typeId, _SizeOfUuid, WD_TYPE_BLOB);
    stmt->store();
    while (stmt->fetch())
    {
        WDObjectSPtr pObject = _core.objectCreator().create(typeId);
        if (pObject == nullptr)
            return nullptr;
        pCom = pObject->toPtr<WDNodeComponent>();
    }
    if (pCom == nullptr)
        return nullptr;

    //��ȡ�������
    WDUuid propertyId;
    ArrayUuid propertyIds;
    stmt = getStmt(SqlSelectCpnPtyId());
    if (!stmt)
        return nullptr;

    stmt->bindParam(0, &componentUuid, _SizeOfUuid, WD_TYPE_BLOB);
    stmt->bindResult(0, &propertyId, _SizeOfUuid, WD_TYPE_BLOB);
    stmt->store();
    WDPropertyGroup tempGrp;
    while (stmt->fetch())
    {
        propertyIds.push_back(propertyId);
    }
    for (const auto& pptId : propertyIds)
    {
        fetchProperty(pptId, tempGrp);
        pGrp = WDProperty::As<WDPropertyGroup>(tempGrp.propertys().front().get());
    }
    if (pGrp == nullptr)
        return nullptr;

    // ��ѯ�Ƿ�������texture
    MapObject mapObj;
    WDUuid textureId;
    ArrayUuid texIds;
    stmt = getStmt(SqlSelectCpnTexId());
    if (stmt)
    {

        stmt->bindParam(0, &componentUuid, _SizeOfUuid, WD_TYPE_BLOB);
        stmt->bindResult(0, &textureId, _SizeOfUuid, WD_TYPE_BLOB);
        stmt->store();
        while (stmt->fetch())
        {
            texIds.push_back(textureId);
        }
        for (const auto& texId : texIds)
        {
            fetchTexture(texId, mapObj);
        }
    }

    const auto&    dataHolder  =   [&mapObj](const WDUuid& id)->WDObject*
    {
        //��mapObj�и���id����
        const auto& itr    =   mapObj.find(id);
        if (itr != mapObj.end())
        {
            return itr->second.get();
        }
        else
            return  nullptr;
    };
    auto pMatGroup = pGrp->findProperty<WDPropertyGroup>("MaterialObject");
    if (pMatGroup != nullptr)
        pMatGroup->setUserData(Data{dataHolder}, "materialDataHolder");
    if(pGrp != nullptr)
        pCom->fromSerialProperties(*pGrp);
    return pCom;
}

void    WDDataBaseTool::fetchTexture(const WDUuid& textureUuid, MapObject& mapObj)
{
    WDStmtSPtr stmt = nullptr;
    WDObjectSPtr pObject = nullptr;

    // ��ѯtypeId
    WDUuid typeId;
    ArrayUuid typeIds;
    stmt = getStmt(SqlSelectTexTypeId());
    if (!stmt)
        return;
    stmt->bindParam(0, &textureUuid, _SizeOfUuid, WD_TYPE_BLOB);
    stmt->bindResult(0, &typeId, _SizeOfUuid, WD_TYPE_BLOB);
    stmt->store();
    while (stmt->fetch())
    {
        typeIds.push_back(typeId);

    }
    for (const auto& tId : typeIds)
    {
        pObject = _core.objectCreator().create(tId);
    }
    if (pObject == nullptr)
        return;

    // ��ѯ����
    WDUuid propertyId;
    ArrayUuid propertyIds;
    stmt = getStmt(SqlSelectTexPtyId());
    if (stmt)
    {
        stmt->bindParam(0, &textureUuid, _SizeOfUuid, WD_TYPE_BLOB);
        stmt->bindResult(0, &propertyId, _SizeOfUuid, WD_TYPE_BLOB);
        stmt->store();
        while (stmt->fetch())
        {
            propertyIds.push_back(propertyId);
        }
        for (const auto& pptId : propertyIds)
        {
            WDPropertyGroup tempGrp;
            fetchProperty(pptId, tempGrp);
            WDPropertyGroup* pGrp = WDProperty::As<WDPropertyGroup>(tempGrp.propertys().front().get());
            if (pGrp != nullptr)
                pObject->fromSerialProperties(*pGrp);
        }
        mapObj[pObject->uuid()] = pObject;
    }
}

bool    WDDataBaseTool::writeNode(WDNodeSPtr pNode)
{
    if (pNode == nullptr)
        return false;
    WDStmtSPtr stmt   =   nullptr;
    WDUuid nodeUuid   =   pNode->uuid();
    WDUuid parentUuid =   pNode->parent() == nullptr ? DefaultUuid() : pNode->parent()->uuid();

    // ��ѯ�ڵ��Ƿ��Ѿ�����
    if (nodeExist(nodeUuid))
        return false;

    //дnodes��
    stmt = getStmt(SqlInsertNodes());
    if (!stmt)
        return false;
    stmt->bindParam(0, &nodeUuid, _SizeOfUuid, WD_TYPE_BLOB);
    stmt->bindParam(1, &parentUuid, _SizeOfUuid, WD_TYPE_BLOB);
    if (!stmt->step())
        return false;

    // дnode2ppt
    WD::WDPropertyGroup ptyGroup;
    pNode->toSerialProperties(ptyGroup);
    stmt = getStmt(SqlInsertNode2Pty());
    if (!stmt)
        return false;
    stmt->bindParam(0, &nodeUuid, _SizeOfUuid, WD_TYPE_BLOB);
    stmt->bindParam(1, &ptyGroup.uuid(), _SizeOfUuid, WD_TYPE_BLOB);
    if (!stmt->step())
        return false;
    writePropertyGroup(&ptyGroup, DefaultUuid());

    // д���
    const auto& components = pNode->components();
    stmt = getStmt(SqlInsertNode2Cpt());
    if (!stmt)
        return false;
    for(const auto& pComm : components)
    {
        stmt->bindParam(0, &nodeUuid, _SizeOfUuid, WD_TYPE_BLOB);
        stmt->bindParam(1, &pComm->uuid(), _SizeOfUuid, WD_TYPE_BLOB);
        if (!stmt->step())
            return false;
        writeComponent(pComm.get());
    }

    // �ݹ��ӽڵ�
    for (size_t i = 0; i < pNode->childCount(); ++i)
    {
        writeNode(WDNode::ToShared(pNode->childAt(i)));
    }
    return true;
}

bool    WDDataBaseTool::writeNodes(const Nodes& nodes, bool useTransaction, bool asRoot)
{
    WDUnused(asRoot);
    if(!isValid())
        return false;
    flush();
    if(useTransaction)
        session()->begin();
    for (const auto& pNode : nodes)
    {
        if (pNode == nullptr)
            continue;
        if(!writeNode(pNode))
        {
            if(useTransaction)
                session()->rollback();
            return false;
        }
    }
    if(useTransaction)
        session()->commit();
    flush();
    return true;
}

bool    WDDataBaseTool::writeNodesFromWdStream(const Stream& stream)
{
    if (stream.empty())
        return  0;

    Nodes   nodes;
    Objects objs;
    WDWdTool::ReadNodesFromWdStream(objs, stream);
    if(objs.empty())
        return false;
    for (auto obj : objs)
    {
        nodes.push_back(obj->toPtr<WDNode>());
    }
    return writeNodes(nodes);
}

bool    WDDataBaseTool::clean()
{
    return session()->truncateAllTables();
};

void    WDDataBaseTool::flush()
{
}


bool    WDDataBaseTool::nodeExist(const char* nodeUuid)
{
    return nodeExist(WDUuid::FromString(nodeUuid));
}

bool    WDDataBaseTool::nodeExist(const WDUuid& nodeUuid)
{
    WDStmtSPtr stmt = getStmt(SqlCountNodeById());
    if(!stmt)
        return false;
    WDUuid id;
    int count = 0;
    stmt->bindParam(0, &nodeUuid, _SizeOfUuid, WD_TYPE_BLOB);
    stmt->bindResult(0, &count, sizeof(count), WD_TYPE_LONG);
    stmt->store();
    while (stmt->fetch())
    {
    }
    return count > 0;
}

bool    WDDataBaseTool::componentExist(const WDUuid& componentUuid)
{
    WDStmtSPtr stmt = getStmt(SqlCountComponentById());
    if(!stmt)
        return false;
    WDUuid id;
    int count = 0;
    stmt->bindParam(0, &componentUuid, _SizeOfUuid, WD_TYPE_BLOB);
    stmt->bindResult(0, &count, sizeof(count), WD_TYPE_LONG);
    stmt->store();
    while (stmt->fetch())
    {
    }
    return count > 0;
}

bool    WDDataBaseTool::textureExist(const WDUuid& textureUuid)
{
    WDStmtSPtr stmt = getStmt(SqlCountTextureById());
    if(!stmt)
        return false;
    WDUuid id;
    int count = 0;
    stmt->bindParam(0, &textureUuid, _SizeOfUuid, WD_TYPE_BLOB);
    stmt->bindResult(0, &count, sizeof(count), WD_TYPE_LONG);
    stmt->store();
    while (stmt->fetch())
    {
    }
    return count > 0;
}

bool    WDDataBaseTool::importFromSqlFile(const char* sqlFileName)
{
    if(!isValid())
        return false;
    return session()->readFromSqlFile(sqlFileName);
}

bool    WDDataBaseTool::importFromWdFile(const char* wdFileName)
{
    if(!isValid())
        return false;
    WDFileReader    fileReader(wdFileName);
    if (fileReader.isBad())
        return false;
    fileReader.readAll();
    WDMemReader     reader(fileReader.data(),fileReader.length());

    WDFormatHeader  header;
    ObjectMap       objects;
    Nodes           nodes;
    /// д���ļ�ͷ
    reader.read(&header,sizeof(header));
    if (_stricmp(header._magic, "wd") != 0)
        return  false;
    reader._dataFunc    =   [&](const WDUuid& id)->WDObject*
    {
        auto itr    =   objects.find(id);
        if (itr == objects.end())
            return  nullptr;
        else
            return  itr->second.get();
    };
    do
    {
        auto    ptr =   WDObject::ReadObject(reader);
        if (ptr.get() == nullptr)
            continue;
        /// ������浽������
        if (dynamic_cast<WDNode*>(ptr.get()))
            nodes.push_back(ptr->toPtr<WDNode>());
        else
            objects[ptr->uuid()]    =   ptr;
        size_t  nPos    =   reader.tell();
        uint    nData   =   0;
        size_t  nRead   =   reader.read(&nData,sizeof(nData));
        if (nRead != sizeof(nData))
            break;

        reader.seek(nPos);

    } while (!reader.isEnd());
    bool ret = writeNodes(nodes);
    return  ret;
}

size_t  WDDataBaseTool::exportToWdFile(const char* wdFileName)
{
    if(!isValid())
        return 0;
    using   ComSet  =   std::map<WDUuid,WDNodeComponentSPtr>;
    using   TexSet  =   std::map<WDUuid,WDTextureSPtr>;
    TexSet  texSets;
    ComSet  comSets;

    Nodes rootNodes;
    getRoots(rootNodes);

    for (const auto& pNode : rootNodes)
    {
        if (pNode == nullptr)
            continue;
        WDNode::RecursionHelpter(*pNode,[&](WDNode& node)
            {
                auto&   coms    =   node.components();
                if (coms.empty())
                    return ;
                for (auto& com : coms)
                {
                    if (com.get() == nullptr)
                        continue;
                    comSets[com->uuid()]    =   com;
                    auto    matCom  =   com->toPtr<WDComponentMaterial>();
                    if (matCom == nullptr)
                        continue;
                    auto    mat     =   matCom->material();
                    if (mat == nullptr)
                        continue;
                    auto    texs    =   mat->textures();
                    for (auto& var : texs)
                    {
                        WDUuid  objId   =   var.second.second->uuid();
                        texSets[objId]  =   var.second.second;
                    }
                }
            });
    }
    auto    fun =   [&](WDWriter& writer)
    {
        WDFormatHeader  header;
        /// ����ͳ����Ҫ���ٴ洢�ռ�
        /// WDFileWriter    writer(pFile);
        /// д���ļ�ͷ
        writer.writeBuffer(&header,sizeof(header));

        /// д����������
        for (auto& var : texSets)
        {
            var.second->toStream(writer);
        }
        /// д���������
        for (auto& var : comSets)
        {
            var.second->toStream(writer);
        }
        /// д��ڵ���Ϣ���ڵ���Ϣ�����������Ϣ�����Ժ�д��
        for (const auto& pNode : rootNodes)
        {
            if (pNode == nullptr)
                continue;
            pNode->toStream(writer);
        }
        return  writer.tell();
    };
    /// ʹ�������ļ�������Ҫ���ڴ��ļ���С
    WDVirtualWriter writer;
    size_t  nWrite  =   fun(writer);
    if (nWrite == 0)
    {   
        return  nWrite;
    }
    /// �����ڴ�
    std::vector<char>   buf(nWrite);
    /// ʹ���ڴ��ļ��������л�����������
    WDMemoryWriter  mem(buf.data(),buf.size());
    /// д�뵽�ڴ��ļ�
    size_t  nWrite1 =   fun(mem);
    if (nWrite != nWrite1)
    {
        /// error
        return  0;
    }
    WDMD5::Key    key   =   WDMD5::Generate(buf.data(),(uint)buf.size());

    /// �����Ŀ����ȷ���ļ�д������ǲ��ɱ��жϵ�
    std::string tmp     =   std::string(wdFileName) + ".tmp";
    /// д�뵽�����ļ�
    FILE*       pFile   =   fopen(tmp.c_str(),"wb");
    if (pFile == nullptr)
        return  0;
    fwrite(buf.data(),1,buf.size(),pFile);
    fclose(pFile);
    /// ɾ��Դ�ļ�
    std::filesystem::remove(wdFileName);
    /// ������tmp�ļ�
    std::filesystem::rename(tmp, wdFileName);
    /// дmd5
    std::string     md5Key      =   std::string(wdFileName) + ".md5";
    FILE*           pMD5File    =   fopen(md5Key.c_str(),"wb");
    if (pMD5File == nullptr)
        return  0;

    fwrite(&key, 1, sizeof(key), pMD5File);
    fclose(pMD5File);

    return  nWrite;
}

bool    WDDataBaseTool::writePropertyGroup(WDPropertyGroup* ptyGroup, const WDUuid& parentUuid)
{
    WDStmtSPtr stmt = nullptr;
    stmt = getStmt(SqlInsertProperties());
    if(!stmt)
        return false;
    WDUuid ptyGrpUuid = ptyGroup->uuid();
    std::string type, name, value;

    type    =   PropertyDataTypeToName(ptyGroup->type());
    name    =   ptyGroup->name();

    stmt->bindParam(0, &ptyGrpUuid, _SizeOfUuid, WD_TYPE_BLOB);
    stmt->bindParam(1, &parentUuid, _SizeOfUuid, WD_TYPE_BLOB);
    stmt->bindParam(2, type.data(), type.length(), WD_TYPE_STRING);
    stmt->bindParam(3, name.data(), name.length(), WD_TYPE_STRING);
    stmt->bindParam(4, value.data(), value.length(), WD_TYPE_STRING);

    if (!stmt->step())
        return false;

    const   auto&   ptys    =   ptyGroup->propertys();//��ȡ������property
    for (const auto& pty : ptys)
    {
        auto childGrp   =   WDProperty::As<WDPropertyGroup>(pty.get());

        if (childGrp != nullptr)//type==Group
        {    
            writePropertyGroup(childGrp, ptyGrpUuid);
        }
        else
        {
            type   =   PropertyDataTypeToName(pty->type());
            name   =   pty->name();
            value  =   pty->valueToString();
            WDUuid childUuid = pty->uuid();

            stmt->bindParam(0, &childUuid, _SizeOfUuid, WD_TYPE_BLOB);
            stmt->bindParam(1, &ptyGrpUuid, _SizeOfUuid, WD_TYPE_BLOB);
            stmt->bindParam(2, type.data(), type.length(), WD_TYPE_STRING);
            stmt->bindParam(3, name.data(), name.length(), WD_TYPE_STRING);

            if (value.length() > _BigDataSize)
            {
                // ����������Ƚϴ󣬿��ǵ����浽һ������
                stmt->bindParam(4, _BigDataTag, strlen(_BigDataTag), WD_TYPE_STRING);
                // д��ר�Ŵ�����ݵı���
                WDStmtSPtr stmtInsertBigData = getStmt(SqlInsertBigData());
                if(!stmtInsertBigData)
                    return false;
                stmtInsertBigData->bindParam(0, &childUuid, _SizeOfUuid, WD_TYPE_BLOB);
                stmtInsertBigData->bindParam(1, value.data(), value.length(), WD_TYPE_LONG_BLOB);
                if(!stmtInsertBigData->step())
                    return false;
            }
            else
            {
                stmt->bindParam(4, value.data(), value.length(), WD_TYPE_STRING);
            }

            if (!stmt->step())
                return false;
        }
    }
    return true;
}

bool    WDDataBaseTool::writeComponent(WDNodeComponent* pCom)
{
    if(pCom == nullptr)
        return false;
    WDUuid componentId =   pCom->uuid();
    // �鿴�Ƿ��Ѿ���������
    if (componentExist(componentId))
        return true;

    WDStmtSPtr stmt = nullptr;

    // дcomponents
    stmt = getStmt(SqlInsertComponents());
    if(!stmt)
        return false;
    WDUuid typeId = pCom->classId();  
    stmt->bindParam(0, &componentId, _SizeOfUuid, WD_TYPE_BLOB);
    stmt->bindParam(1, &typeId, _SizeOfUuid, WD_TYPE_BLOB);
    if (!stmt->step())
        return false;

    // д����
    WD::WDPropertyGroup cpnPtyGroup;
    stmt = getStmt(SqlInsertCpt2Pty());
    if(!stmt)
        return false;
    stmt->bindParam(0, &componentId, _SizeOfUuid, WD_TYPE_BLOB);
    stmt->bindParam(1, &cpnPtyGroup.uuid(), _SizeOfUuid, WD_TYPE_BLOB);
    if (!stmt->step())
        return false;

    pCom->toSerialProperties(cpnPtyGroup);
    if (!writePropertyGroup(&cpnPtyGroup, DefaultUuid()))
        return false;

    // ����texture
    if (!writeTexture(pCom))
        return false;
    return true;
}

bool    WDDataBaseTool::writeTexture(WDNodeComponent* pCom)
{
    if(pCom == nullptr)
        return false;
    WDComponentMaterial* pComMat = WDNodeComponent::As<WDComponentMaterial>(pCom);
    if (pComMat == nullptr)
        return false;
    auto pMat = pComMat->material();
    if(pMat == nullptr)
        return false;

    WDStmtSPtr stmt1  =   getStmt(SqlInsertCpt2Tex());
    WDStmtSPtr stmt2  =   getStmt(SqlInsertTextures());
    WDStmtSPtr stmt3  =   getStmt(SqlInsertTex2Pty());
    if (!stmt1 || !stmt2 || !stmt3)
        return false;

    WDMaterial::Textures    textures=   pMat->textures();
    for (const auto& tex : textures)
    {
        const auto&   pTex    =   tex.second.second;
        auto   texId   =   pTex->uuid();
        stmt1->bindParam(0, &pCom->uuid(), _SizeOfUuid, WD_TYPE_BLOB);
        stmt1->bindParam(1, &texId, _SizeOfUuid, WD_TYPE_BLOB);
        if (!stmt1->step())
            return false;

        // ����Ƿ��Ѿ�д���texture
        if (textureExist(texId))
            continue;
        auto   typeId  =   pTex->classId();
        stmt2->bindParam(0, &texId, _SizeOfUuid, WD_TYPE_BLOB);
        stmt2->bindParam(1, &typeId, _SizeOfUuid, WD_TYPE_BLOB);
        if (!stmt2->step())
            return false;

        WD::WDPropertyGroup texPtyGroup;
        stmt3->bindParam(0, &texId, _SizeOfUuid, WD_TYPE_BLOB);
        stmt3->bindParam(1, &texPtyGroup.uuid(), _SizeOfUuid, WD_TYPE_BLOB);
        if (!stmt3->step())
            return false;
        pTex->toSerialProperties(texPtyGroup);

        if (!writePropertyGroup(&texPtyGroup, DefaultUuid()))
            return false;
    }
    return true;
}

bool    WDDataBaseTool::deleteNodes(const Strings& nodeUuids, bool useTransaction)
{
    if(!isValid())
        return false;
    if(useTransaction)
        session()->begin();
    for (const auto& nodeUuid : nodeUuids)
    {
        if (!deleteNode(nodeUuid.c_str()))
        {
            if(useTransaction)
                session()->rollback();
            return false;
        }
    }
    if(useTransaction)
        session()->commit();
    return true;
}

bool    WDDataBaseTool::deleteNode(const WDUuid& nodeUuid)
{
    if (!nodeExist(nodeUuid))
        return false;

    WDStmtSPtr stmt = getStmt(SqlDelNodeById());
    if(!stmt)
        return false;
    stmt->bindParam(0, &nodeUuid, _SizeOfUuid, WD_TYPE_BLOB);
    if (!stmt->step())
        return false;

    // ɾ���ӽڵ�
    // ��ѯ�ӽڵ�
    ArrayUuid childUuids;
    WDUuid childUuid;
    stmt = getStmt(SqlSelectNodeChildId());
    if(!stmt)
        return false;
    stmt->bindParam(0, &nodeUuid, _SizeOfUuid, WD_TYPE_BLOB);
    stmt->bindResult(0, &childUuid, _SizeOfUuid, WD_TYPE_BLOB);
    stmt->store();
    while (stmt->fetch())
    {
        childUuids.push_back(childUuid);
    }
    for (const auto& cUid : childUuids)
    {
        if (!deleteNode(cUid))
            return false;
    }

    // ɾ������
    // ��ѯ����
    WDUuid ptyUuid;
    stmt = getStmt(SqlSelectNodePtyId());
    if(!stmt)
        return false;
    stmt->bindParam(0, &nodeUuid, _SizeOfUuid, WD_TYPE_BLOB);
    stmt->bindResult(0, &ptyUuid, _SizeOfUuid, WD_TYPE_BLOB);
    stmt->store();
    while (stmt->fetch())
    {
        if(!deleteProperty(ptyUuid))
            return false;
    }
    // ɾ��node-pty��ϵ
    stmt = getStmt(SqlDelNodePtyByNodeId());
    if(!stmt)
        return false;
    stmt->bindParam(0, &nodeUuid, _SizeOfUuid, WD_TYPE_BLOB);
    if(!stmt->step())
        return false;

    // ɾ�����
    // ��ѯ���
    WDUuid cpnUuid;
    stmt = getStmt(SqlSelectNodeCpnId());
    WDStmtSPtr stmt2 = getStmt(SqlCountNodeByCpnId());
    if(!stmt || !stmt2)
        return false;
    stmt->bindParam(0, &nodeUuid, _SizeOfUuid, WD_TYPE_BLOB);
    stmt->bindResult(0, &cpnUuid, _SizeOfUuid, WD_TYPE_BLOB);
    stmt->store();
    while (stmt->fetch())
    {
        // ��ѯ�Ƿ�Ϊ�������
        int nodeCount = 0;
        stmt2->bindParam(0, &cpnUuid, _SizeOfUuid, WD_TYPE_BLOB);
        stmt2->bindResult(0, &nodeCount, sizeof(nodeCount), WD_TYPE_LONG);
        stmt2->store();
        stmt2->fetch();
        if (nodeCount > 1)
            continue;
        // ���������������ɾ��
        if (!deleteComponent(cpnUuid))
            return false;
    }
    // ɾ��node-cpn��ϵ
    stmt = getStmt(SqlDelNodeCpnByNodeId());
    if(!stmt)
        return false;
    stmt->bindParam(0, &nodeUuid, _SizeOfUuid, WD_TYPE_BLOB);
    if(!stmt->step())
        return false;

    return true;
}

bool    WDDataBaseTool::replaceNode(WDNodeSPtr node)
{
    if (!isValid())
        return false;
    session()->begin();
    // ��ɾ��
    if (!deleteNodes({node->uuid().toString()}, false))
    {
        session()->rollback();
        return false;
    }
    if (!writeNodes({node}, false))
    {
        session()->rollback();
        return false;
    }
    session()->commit();
    return true;
}

bool    WDDataBaseTool::deleteProperty(const WDUuid& ptyUuid)
{
    WDStmtSPtr stmt = getStmt(SqlDelPtyById());
    if (!stmt)
        return false;
    stmt->bindParam(0, &ptyUuid, _SizeOfUuid, WD_TYPE_BLOB);
    if(!stmt->step())
        return false;

    // ɾ��bigdata
    stmt = getStmt(SqlDelBigDataByPtyId());
    stmt->bindParam(0, &ptyUuid, _SizeOfUuid, WD_TYPE_BLOB);
    if(!stmt->step())
        return false;

    // ɾ��������
    // ��ѯ������
    WDUuid childUuid;
    ArrayUuid childUuids;
    auto strUid = ptyUuid.toString();
    stmt = getStmt(SqlSelectPtyChildInfo());
    if(!stmt)
        return false;
    stmt->bindParam(0, &ptyUuid, _SizeOfUuid, WD_TYPE_BLOB);
    stmt->bindResult(0, &childUuid, _SizeOfUuid, WD_TYPE_BLOB);
    stmt->bindResult(1, nullptr, 0, WD_TYPE_STRING);
    stmt->bindResult(2, nullptr, 0, WD_TYPE_STRING);
    stmt->bindResult(3, nullptr, 0, WD_TYPE_STRING);
    stmt->store();
    while (stmt->fetch())
    {
        childUuids.push_back(childUuid);
    }
    for (const auto& cUid : childUuids)
    {
        if(!deleteProperty(cUid))
            return false;
    }
    return true;
}

bool    WDDataBaseTool::deleteComponent(const WDUuid& cpnUuid)
{
    WDStmtSPtr stmt = getStmt(SqlDelCpnById());
    if (!stmt)
        return false;
    stmt->bindParam(0, &cpnUuid, _SizeOfUuid, WD_TYPE_BLOB);
    if (!stmt->step())
        return false;

    // ɾ������
    // ��ѯ����
    WDUuid ptyUuid;
    stmt = getStmt(SqlSelectCpnPtyId());
    if(!stmt)
        return false;
    stmt->bindParam(0, &cpnUuid, _SizeOfUuid, WD_TYPE_BLOB);
    stmt->bindResult(0, &ptyUuid, _SizeOfUuid, WD_TYPE_BLOB);
    stmt->store();
    while (stmt->fetch())
    {
        if (!deleteProperty(ptyUuid))
            return false;
    }
    // ɾ��cpn-pty��ϵ
    stmt = getStmt(SqlDelCpnPtyByCpnId());
    if(!stmt)
        return false;
    stmt->bindParam(0, &cpnUuid, _SizeOfUuid, WD_TYPE_BLOB);
    if(!stmt->step())
        return false;

    // ɾ��tex
    WDUuid texUuid;
    stmt = getStmt(SqlSelectCpnTexId());
    WDStmtSPtr stmt2 = getStmt(SqlCountCpnByTexId());
    if(!stmt || !stmt2)
        return false;
    stmt->bindParam(0, &cpnUuid, _SizeOfUuid, WD_TYPE_BLOB);
    stmt->bindResult(0, &texUuid, _SizeOfUuid, WD_TYPE_BLOB);
    stmt->store();
    while (stmt->fetch())
    {
        // ��ѯ�Ƿ�����tex
        int cpnCount = 0;
        stmt2->bindParam(0, &texUuid, _SizeOfUuid, WD_TYPE_BLOB);
        stmt2->bindResult(0, &cpnCount, sizeof(cpnCount), WD_TYPE_LONG);
        stmt2->store();
        stmt2->fetch();
        if (cpnCount > 1)
            continue;
        if (!deleteTexture(texUuid))
            return false;
    }
    // ɾ��cpn-tex��ϵ
    stmt = getStmt(SqlDelCpnTexByCpnId());
    if (!stmt)
        return false;
    stmt->bindParam(0, &cpnUuid, _SizeOfUuid, WD_TYPE_BLOB);
    if(!stmt->step())
        return false;

    return true;
}

bool    WDDataBaseTool::deleteTexture(const WDUuid& texUuid)
{
    WDStmtSPtr stmt = getStmt(SqlDelTexById());
    if(!stmt)
        return false;
    stmt->bindParam(0, &texUuid, _SizeOfUuid, WD_TYPE_BLOB);
    if(!stmt->step())
        return false;

    // ɾ������
    // ��ѯ����
    WDUuid ptyUuid;
    stmt = getStmt(SqlSelectTexPtyId());
    if (!stmt)
        return false;
    stmt->bindParam(0, &texUuid, _SizeOfUuid, WD_TYPE_BLOB);
    stmt->bindResult(0, &ptyUuid, _SizeOfUuid, WD_TYPE_BLOB);
    stmt->store();
    while (stmt->fetch())
    {
        // ɾ������
        if (!deleteProperty(ptyUuid))
            return false;
    }
    // ɾ��tex-pty��ϵ
    stmt = getStmt(SqlDelTexPtyByTexId());
    if(!stmt)
        return false;
    stmt->bindParam(0, &texUuid, _SizeOfUuid, WD_TYPE_BLOB);
    if(!stmt->step())
        return false;
    return true;
}


size_t  WDDataBaseTool::getRoots(Nodes& nodes)
{
    WDStmtSPtr stmt = getStmt(SqlSelctRootNodes());
    if(!stmt)
        return false;
    Strings ids;
    WDUuid nodeUuid;

    stmt->bindResult(0, &nodeUuid, _SizeOfUuid, WD_TYPE_BLOB);
    stmt->store();
    while (stmt->fetch())
    {
        ids.push_back(nodeUuid.toString());
    }

    Objects objs;
    fetchNodesByNodeUuid(objs, ids);
    for (const auto& obj : objs)
    {
        auto pNode = obj->toPtr<WDNode>();
        if (pNode != nullptr)
            nodes.push_back(pNode);
    }

    return nodes.size();
}



bool    WDDataBaseTool::tablesExist()
{
    if(!isValid())
        return false;
    auto tables = session()->getTables();
    for (int i = 0; i < tables.size(); i++)
    {
        // ͨ������������lower������Ϊ�˱���
        tables[i] = StringToLower(tables[i]);
    }
    for (const auto& tb : Strings{NodeTableName()
                                , ComponentTableName()
                                , PropertyTableName()
                                , TextureTableName()
                                , Node2PropertyTableName()
                                , Component2PropertyTableName()
                                , Texture2PropertyTableName()
                                , Node2ComponentTableName()
                                , Component2TextureTableName()
                                , BigDataTableName()})
    {
        auto itr = std::find(tables.begin(), tables.end(), StringToLower(tb));
        if(itr == tables.end())
            return false;
    }

    return true;
}

bool    WDDataBaseTool::saveToFile(const char* fileName)
{
    if (!isValid())
        return false;
    return session()->saveToFile(fileName);
}

bool    WDDataBaseTool::loadRefNodeForBranch(WDNode& node, MapNodes& refNodes)
{
    return false;
#if 0
    bool tag = true;
    WDBMRefCollector collector;
    // �ռ��ڵ��֧�µ����нڵ����ù�ϵ
    WDNode::RecursionHelpter(node, [](WDBMRefCollector& collector, WDNode& node)
        {
            // �ռ����ݶ���Ľڵ�����
            auto pBase = WD::WDBMBase::GetBase(node);
            if (pBase != nullptr)
                pBase->collectNodeRef(collector);
        }, collector);

    //�����ֵ�
    collector.updateDictRefs(_core.enumDictionaryMgr());

    // ��ȡԪ����/��ƿ��refNode id
    using Refs      =   std::list< WDBMNodeRef*>;
    using RefsMap   =   std::map<WDUuid, Refs>;
    const auto& cRefs     =   collector.cRefs(); // ��Ԫ����ڵ�����ù�ϵ
    const auto& dRefs     =   collector.dRefs(); // ����ƿ�ڵ�����ù�ϵ

    auto func = [&](const RefsMap& refsMap)
    {
        for (auto itr : refsMap)
        {
            WDUuid refNodeId = itr.first;

            if (refNodes.find(refNodeId) == refNodes.end())
            {
                // ������û��
                auto nodeOut = fetchNode(refNodeId);
                if (nodeOut == nullptr)
                {
                    // �ýڵ�û�鵽
                    tag = false;
                    continue;
                }

                if (!loadRefNodeForBranch(*nodeOut, refNodes))
                {
                    // refNode��refNode����ʧ��
                    tag = false;
                    continue;
                }

                refNodes[refNodeId] = nodeOut;
            }

            auto refNode = refNodes[refNodeId];

            auto hostNodes = itr.second;
            for (auto hn : hostNodes)
            {
                hn->setRefNode(refNode); // ����refNode
            }
        }
    };

    func(cRefs);
    func(dRefs);

    // �ݹ�������ݶ���Ľڵ�����
    WDNode::RecursionHelpter(node, [](WDCore& app, WDNode& node)
        {
            auto pBase = WD::WDBMBase::GetBase(node);
            if (pBase == nullptr)
                return;
            // �������ݶ������
            pBase->update(app, node);
        }, _core);

    return tag;
#endif
}

WDDataBaseTool::ArrayUuid   WDDataBaseTool::getObjectUuidByName(const char* name)
{
    ArrayUuid uids;

    const char* proto   =   "SELECT value FROM `%s` WHERE type = ? and name = ? and \
                            pId IN (SELECT pId FROM `%s` WHERE type = ? and name = ? and value = ?)";
    char sql[MAX_SQL_LEN];
    sprintf_s(sql, proto, PropertyTableName(), PropertyTableName());
    WDStmtSPtr stmt     =   getStmt(sql);
    if (!stmt)
        return uids;

    stmt->bindParam(0, "Guid", 4, WD_TYPE_STRING);
    stmt->bindParam(1, "id", 2, WD_TYPE_STRING);
    stmt->bindParam(2, "String", 6, WD_TYPE_STRING);
    stmt->bindParam(3, "name", 4, WD_TYPE_STRING);
    stmt->bindParam(4, name, strlen(name), WD_TYPE_STRING);

    stmt->bindResult(0, nullptr, 0, WD_TYPE_BLOB);
    stmt->store();

    int strUidSize = WDUuid{}.toString().length();
    std::string  value;
    value.resize(strUidSize);
    while (stmt->fetch())
    {
        // ��ȡ����
        size_t valueSize    =   stmt->columnDataSize(0);
        if (valueSize == strUidSize)
        {
            if(stmt->fetchColumn(0, value.data(), valueSize, 0))
                uids.push_back(WDUuid::FromString(value));
        }
    }
    return uids;
}

WDDataBaseTool::Strings WDDataBaseTool::getObjectNameByUuid(const char* uuid)
{
    Strings names;
    const char* proto   =   "SELECT value FROM `%s` WHERE type = ? and name = ? and \
                            pId IN (SELECT pId FROM `%s` WHERE type = ? and name = ? and value = ?)";
    char sql[MAX_SQL_LEN];
    sprintf_s(sql, proto, PropertyTableName(), PropertyTableName());
    WDStmtSPtr stmt     =   getStmt(sql);
    if (!stmt)
        return names;

    stmt->bindParam(0, "String", 6, WD_TYPE_STRING);
    stmt->bindParam(1, "name", 4, WD_TYPE_STRING);
    stmt->bindParam(2, "Guid", 4, WD_TYPE_STRING);
    stmt->bindParam(3, "id", 2, WD_TYPE_STRING);
    stmt->bindParam(4, uuid, strlen(uuid), WD_TYPE_STRING);

    stmt->bindResult(0, nullptr, 0, WD_TYPE_BLOB);
    stmt->store();
    std::string  name;
    while (stmt->fetch())
    {
        // ��ȡ����
        size_t valueSize    =   stmt->columnDataSize(0);
        if (valueSize)
        {
            name.resize(valueSize);
            stmt->fetchColumn(0, name.data(), valueSize, 0);
        }
        names.push_back(name);
    }
    return names;
}

bool    WDDataBaseTool::fetchRootProperty(const WDUuid& propertyId, WDUuid& rootPtyId)
{
    WDStmtSPtr stmt = getStmt(SqlSelectParentPty());
    if (!stmt)
        return false;
    WDUuid pId;
    stmt->bindParam(0, &propertyId, _SizeOfUuid, WD_TYPE_BLOB);
    stmt->bindResult(0, &pId, _SizeOfUuid, WD_TYPE_BLOB);
    stmt->store();
    if(!stmt->fetch())
        return false;
    if (pId != DefaultUuid())
    {
        if(!fetchRootProperty(pId, rootPtyId))
            return false;
    }
    else
    {
        rootPtyId = propertyId;
    }
    return true;;
}

bool    WDDataBaseTool::fetchNodesByNodeName(Objects& objs, const Strings& names)
{
    Strings nodeIds;
    for (const auto& value : names)
    {
        auto objIds = getObjectUuidByName(value.c_str());
        if (objIds.empty())
            continue;
        for (const auto& id : objIds)
        {
            nodeIds.push_back(id.toString());
        }
    }

    fetchNodesByNodeUuid(objs, nodeIds);
    return true;
}

WD_NAMESPACE_END