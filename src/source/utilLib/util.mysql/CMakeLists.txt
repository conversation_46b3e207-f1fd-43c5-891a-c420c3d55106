set(TARGET_NAME util.mysql)

set(HEADER_FILES
	"WDMysqlStmt.h"
	"WDMysqlSession.h"
	"WDMysqlDataBase.h"
	"WDMysqlTool.h"
	"WDProjectUseMysql.h"
	"WDMysqlApiDefine.h"
	"WDMysqlHeadFiles.h"
)

set(SOURCE_FILES
	"WDMysqlStmt.cpp"
	"WDMysqlSession.cpp"
	"WDMysqlDataBase.cpp"
	"WDMysqlTool.cpp"
	"WDProjectUseMysql.cpp"
)

add_library(${TARGET_NAME} SHARED
		${HEADER_FILES}
		${SOURCE_FILES}
)

target_include_directories(${TARGET_NAME} PRIVATE ${CMAKE_CURRENT_SOURCE_DIR})

target_compile_definitions(${TARGET_NAME} PRIVATE
	-DMYSQL_EXPORTS
)

target_link_libraries(${TARGET_NAME} PRIVATE wizDesignerCore libmysql util.database prsdk)

CopyImportedRuntimeDependency(${TARGET_NAME})