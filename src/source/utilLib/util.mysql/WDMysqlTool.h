#pragma once
#include "WDMysqlDataBase.h"
#include "WDMysqlApiDefine.h"

WD_NAMESPACE_BEGIN

WD_DECL_CLASS_UUID(MysqlTool, "98083D89-E390-4CD9-A231-0ED34B859CFD");

class MYSQL_API MysqlTool : public WDDataBaseTool
{
    WD_DECL_OBJECT(MysqlTool)
public:
    MysqlTool(WDCore& core, const Param& param, bool isCatalog);

    MysqlTool(WDCore& core
            , const char* hostName
            , const char* usrName
            , const char* password
            , const char* dbName
            , int port
            , const char* charSet
            , bool isCatalog = false);

    MysqlTool(WDCore& core, WDDataBase* database, bool isCatalog);

    ~MysqlTool();
public:
    /**
     * @brief �������ݿ�
     * @param param ���ݿ����Ӳ���
     * @return �Ƿ����ӳɹ�
    */
    virtual bool connectDB(const Param& param) override;
    /**
    * @brief ����ҵ������������ӵ����ݿ�󣬴���������д��
    */
    virtual bool createTables() override;
};


WD_DECL_OBJECT_SMATR_POINTER(MysqlTool)


static const constexpr char CreateBussinessTablesInMysql[] = "\
    \
    SET FOREIGN_KEY_CHECKS=0;\
    \
    DROP TABLE IF EXISTS `bigdatas`;\
    CREATE TABLE `bigdatas` (\
        `propertyId` binary(16) NOT NULL,\
        `data` longblob,\
        PRIMARY KEY (`propertyId`)\
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;\
    \
    DROP TABLE IF EXISTS `component2property`;\
    CREATE TABLE `component2property` (\
        `componentId` binary(16) NOT NULL,\
        `propertyId` binary(16) NOT NULL,\
        PRIMARY KEY (`componentId`)\
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;\
    \
    DROP TABLE IF EXISTS `component2texture`;\
    CREATE TABLE `component2texture` (\
        `componentId` binary(16) NOT NULL,\
        `textureId` binary(16) NOT NULL\
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;\
    \
    DROP TABLE IF EXISTS `components`;\
    CREATE TABLE `components` (\
        `objId` binary(16) NOT NULL,\
        `typeId` binary(16) NOT NULL,\
        PRIMARY KEY (`objId`)\
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;\
    \
    DROP TABLE IF EXISTS `node2component`;\
    CREATE TABLE `node2component` (\
        `nodeId` binary(16) NOT NULL,\
        `componentId` binary(16) NOT NULL\
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;\
    \
    DROP TABLE IF EXISTS `node2property`;\
    CREATE TABLE `node2property` (\
        `nodeId` binary(16) NOT NULL,\
        `propertyId` binary(16) NOT NULL,\
        PRIMARY KEY (`nodeId`)\
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;\
    \
    DROP TABLE IF EXISTS `nodes`;\
    CREATE TABLE `nodes` (\
        `objId` binary(16) NOT NULL,\
        `pId` binary(16) NOT NULL\
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;\
    \
    DROP TABLE IF EXISTS `properties`;\
    CREATE TABLE `properties` (\
        `objId` binary(16) NOT NULL,\
        `pId` binary(16) NOT NULL,\
        `type` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,\
        `name` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,\
        `value` varchar(1024) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,\
        PRIMARY KEY (`objId`)\
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;\
    \
    DROP TABLE IF EXISTS `texture2property`;\
    CREATE TABLE `texture2property` (\
        `textureId` binary(16) NOT NULL,\
        `propertyId` binary(16) NOT NULL,\
        PRIMARY KEY (`textureId`)\
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;\
    \
    DROP TABLE IF EXISTS `textures`;\
    CREATE TABLE `textures` (\
        `objId` binary(16) NOT NULL,\
        `typeId` binary(16) NOT NULL,\
        PRIMARY KEY (`objId`)\
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;\
    \
    \
    CREATE INDEX `cpnid` ON `component2texture`(`componentId`);\
    CREATE INDEX `nodeid` ON `node2component`(`nodeId`);\
    CREATE INDEX `pid` ON `nodes`(`pid`);\
    CREATE INDEX `objid` ON `nodes`(`objid`);\
    CREATE INDEX `pid` ON `properties`(`pId`);\
    CREATE INDEX `value` ON `properties`(`value`);\
    ";


WD_NAMESPACE_END