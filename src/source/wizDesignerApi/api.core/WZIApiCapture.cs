using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;
using WD;

namespace WD
{
    public class WDCaptureApi
    {
        private const string DllName = @"api.core.dll";
        //private const string DllName = @"D:\work\wizdesignernew\src\bin\WindowsDebug\api.core.dll";
        /// <summary>
        /// 激活捕捉工具,释放的时候需要把返回值传递给 wdDeativateCapture
        /// </summary>
        /// <param name="notify"></param>
        /// <returns></returns>
        [DllImport(DllName, CallingConvention = CallingConvention.Cdecl)]
        public static extern IntPtr wdActiveCapture(ref CaptureNotify notify);
        /// <summary>
        /// 禁止激活，
        /// </summary>
        /// <param name="capture">参数来自wdActiveCapture 返回值</param>
        /// <returns></returns>
        [DllImport(DllName, CallingConvention = CallingConvention.Cdecl)]
        public static extern void   wdDeativateCapture(IntPtr capture);
    }
}
