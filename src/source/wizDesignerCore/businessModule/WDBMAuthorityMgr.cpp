#include "WDBMAuthorityMgr.h"
#include "WDBMBase.h"

WD_NAMESPACE_BEGIN

WDBMAuthorityMgr::WDBMAuthorityMgr(WDBMBase& bmBase)
    : _bmBase(bmBase)
{
    // 默认禁用权限校验
    _enabled = false;
    // 原子单位的节点深度，默认为-1代表不生效
    _atomicDepth = -1;
}
WDBMAuthorityMgr::~WDBMAuthorityMgr()
{
}

bool WDBMAuthorityMgr::checkAdd(const WDNode::Nodes& pParents)
{
    if (!_enabled)
        return true;

    for (const auto& pParent : pParents)
    {
        if (pParent == nullptr)
            continue;

        // 校验权限，有一个校验不通过就直接失败
        if (!pParent->flags().hasFlag(WDNode::Flag::F_Editable))
            return false;
    }

    return true;
}
bool WDBMAuthorityMgr::checkModify(const WDNode::Nodes& nodes, bool inherit, bool lock)
{
    if (!_enabled)
        return true;

    bool success = true;
    WDNode::Nodes pLockNodes;
    pLockNodes.reserve(nodes.size());
    for (const auto& pNode : nodes)
    {
        if (pNode == nullptr)
            continue;

        if (inherit)
        {
            // 递归校验权限，有一个校验不通过就代表失败
            WDNode::RecursionHelpterR(*pNode, [&success](WDNode& node) {
                    if (!node.flags().hasFlag(WDNode::Flag::F_Editable))
                    {
                        success = false;
                        return true;
                    }
                    return false;
                });
        }
        else
        {
            // 校验权限，有一个校验不通过就代表失败
            if (!pNode->flags().hasFlag(WDNode::Flag::F_Editable))
            {
                success = false;
                continue;
            }
        }

        pLockNodes.push_back(pNode);
    }

    // 尝试加锁
    bool lockRes = true;
    if (lock)
    {
        lockRes = this->lock(pLockNodes, inherit);
    }

    return success && lockRes;
}
bool WDBMAuthorityMgr::checkDelete(const WDNode::Nodes& nodes, bool lock)
{
    if (!_enabled)
        return true;

    bool success = true;
    WDNode::Nodes pLockNodes;
    pLockNodes.reserve(nodes.size());
    for (const auto& pNode : nodes)
    {
        if (pNode == nullptr)
            continue;

        // 递归校验权限，有一个校验不通过就代表失败
        WDNode::RecursionHelpterR(*pNode, [&success](WDNode& node) {
                if (!node.flags().hasFlag(WDNode::Flag::F_Editable))
                    return true;
                return false;
            });

        pLockNodes.push_back(pNode);
    }

    // 尝试加锁
    bool lockRes = true;
    if (lock)
    {
        lockRes = this->lock(pLockNodes, true);
    }

    return success && lockRes;
}
bool WDBMAuthorityMgr::checkMove(const WDNode::Nodes& pParents)
{
    if (!_enabled)
        return true;

    for (const auto& pParent : pParents)
    {
        if (pParent == nullptr)
            continue;

        // 校验权限，有一个校验不通过就直接失败
        if (!pParent->flags().hasFlag(WDNode::Flag::F_Editable))
            return false;
    }

    return true;
}

bool WDBMAuthorityMgr::lock(const std::vector<LockItem>& items)
{
    if (!_enabled)
        return true;

    // 未指定加锁回调，不可能成功加锁节点
    if (!_funcLockBefore)
    {
        assert(false);
        return false;
    }

    // 权限校验，全部通过才算通过，有一个不通过即为不通过
    bool editable = true;
    // 筛选出需要加锁的节点，比如一个SITE
    std::vector<LockItem> needLockNodes;
    // 预测加锁的实例节点，比如SITE下的所有深度小于最小单元深度的子孙节点
    WDNode::Nodes predInsts;
    for (const auto& item : items)
    {
        const auto& [pWNode, inherit] = item;
        auto pNode = pWNode.lock();
        if (pNode == nullptr)
            continue;
        // 如果节点有创建标志，则直接忽略加锁
        if (pNode->flags().hasFlag(WDNode::F_NewCreated))
            continue;
        if (inherit)
        {
            // 递归获取预测加锁的实例节点，若已加锁个数和实例节点个数相同则代表此次递归的根节点已被加锁，无需再进行加锁
            size_t lockedCnt = 0;
            WDNode::Nodes tPredInsts;
            this->recursionLockInsts(pNode, pNode->depth(), tPredInsts, lockedCnt);
            if (lockedCnt != tPredInsts.size())
            {
                if (tPredInsts.size() > 1 )
                    needLockNodes.push_back(item);
                else
                    needLockNodes.emplace_back(this->lockableNode(pNode), inherit);

                predInsts.insert(predInsts.end(), tPredInsts.begin(), tPredInsts.end());
            }
        }
        else
        {
            // 获取可加锁的节点
            auto pLockableNode = this->lockableNode(pNode);
            if (pLockableNode == nullptr)
                continue;
            // 查询是否已加锁
            if (this->queryLockNode(pLockableNode->getRemoteId()))
                continue;

            needLockNodes.emplace_back(pLockableNode, inherit);
            predInsts.push_back(pLockableNode);
        }
    }

    auto sucIds = _funcLockBefore(needLockNodes);

    // 这里保存加锁成功的节点，并作整体成功失败的判断
    for (const auto& pInst : predInsts)
    {
        if (pInst == nullptr)
            continue;
        if (sucIds.find(pInst->getRemoteId()) == sucIds.end())
        {
            editable = false;
            continue;
        }

        _pLockNodes[pInst->getRemoteId()] = pInst;
    }

    // 如果需要加锁的节点全部被加锁且权限校验全部通过，则认为加锁成功
    return editable && sucIds.size() == predInsts.size();
}
bool WDBMAuthorityMgr::unlock(const std::vector<LockItem>& items)
{
    if (!_enabled)
        return true;

    // 未指定解锁回调，不可能成功解锁节点
    if (!_funcUnlockBefore)
    {
        assert(false);
        return false;
    }

    // 筛选出需要解锁的节点，比如一个SITE
    std::vector<LockItem> needUnlockNodes;
    // 预测解锁锁的实例节点，比如SITE下的所有深度小于最小单元深度的子孙节点
    WDNode::Nodes predInsts;
    for (const auto& item : items)
    {
        const auto& [pWNode, inherit] = item;
        auto pNode = pWNode.lock();
        if (pNode == nullptr)
            continue;
        if (inherit)
        {
            // 递归获取预测解锁的实例节点，若未加锁个数和实例节点个数相同则代表此次递归的根节点未被加锁，无需再进行解锁
            size_t unlockedCnt = 0;
            WDNode::Nodes tPredInsts;
            this->recursionUnlockInsts(pNode, pNode->depth(), tPredInsts, unlockedCnt);

            if (unlockedCnt != tPredInsts.size())
            {
                if (tPredInsts.size() > 1 )
                    needUnlockNodes.push_back(item);
                else
                    needUnlockNodes.emplace_back(this->lockableNode(pNode), inherit);

                predInsts.insert(predInsts.end(), tPredInsts.begin(), tPredInsts.end());
            }
        }
        else
        {
            // 获取可解锁的节点
            auto pLockableNode = this->lockableNode(pNode);
            if (pLockableNode == nullptr)
                continue;

            needUnlockNodes.emplace_back(pLockableNode, inherit);
            predInsts.push_back(pLockableNode);
        }
    }

    bool bCancelCheckOut = false;
    auto sucIds = _funcUnlockBefore(needUnlockNodes, bCancelCheckOut);
    if (bCancelCheckOut)
        return false;

    // 这里去除解锁成功的节点，并作整体成功失败的判断
    for (const auto& pInst : predInsts)
    {
        if (pInst == nullptr)
            continue;
        if (sucIds.find(pInst->getRemoteId()) == sucIds.end())
            continue;

        _pLockNodes.erase(pInst->getRemoteId());
    }

    // 如果需要解锁的节点全部被解锁且权限校验全部通过，则认为解锁成功
    return sucIds.size() == predInsts.size();
}
bool WDBMAuthorityMgr::unlockAll(bool& bCancelCheckOut)
{
    if (!_enabled)
        return true;

    // 未指定解锁回调，不可能成功解锁节点
    if (!_funcUnlockBefore)
    {
        assert(false);
        return false;
    }

    // 需要解锁的节点
    auto pRoot = _bmBase.root();
    if (pRoot == nullptr)
        return false;
    LockItem rootItem(pRoot, true);
    // 递归获取预测解锁的实例节点，若未加锁个数和实例节点个数相同则代表此次递归的根节点未被加锁，无需再进行解锁，返回失败
    size_t unlockedCnt = 0;
    WDNode::Nodes predInsts;
    this->recursionUnlockInsts(pRoot, pRoot->depth(), predInsts, unlockedCnt);
    if (unlockedCnt == predInsts.size())
        return false;

    auto sucIds = _funcUnlockBefore({ rootItem }, bCancelCheckOut);
    if (bCancelCheckOut)
        return false;

    // 这里去除解锁成功的节点，并作整体成功失败的判断
    for (const auto& pInst : predInsts)
    {
        if (pInst == nullptr)
            continue;
        if (sucIds.find(pInst->getRemoteId()) == sucIds.end())
            continue;

        _pLockNodes.erase(pInst->getRemoteId());
    }

    // 如果需要解锁的节点全部被解锁且权限校验全部通过，则认为解锁成功
    return sucIds.size() == predInsts.size();
}
inline WDNode::Nodes WDBMAuthorityMgr::lockNodes() const
{
    WDNode::Nodes result;
    for (const auto& [rid, pLockNode] : _pLockNodes)
    {
        result.push_back(pLockNode.lock());
    }
    return result;
}

void WDBMAuthorityMgr::initEditableNodes(const WDNode::Nodes& pNodes)
{
    if (pNodes.empty())
        return ;

    for (const auto& pNode : pNodes)
    {
        if (pNode == nullptr)
            continue;

        // 递归设置可编辑标志
        WDNode::RecursionHelpter(*pNode, [](WDNode& node) {
            auto flags = node.flags();
            flags.setFlag(WD::WDNode::Flag::F_Editable, true);
            node.setFlags(flags);
        });
    }
}
void WDBMAuthorityMgr::cacheDelNodes(const WDNode::Nodes& pNodes)
{
    if (pNodes.empty())
        return ;

    for (const auto& pNode : pNodes)
    {
        if (pNode == nullptr)
            continue;

        bool bFind = false;
        auto pParent = pNode;
        while (pParent != nullptr)
        {
            if (_pDelNodes.find(pParent->getRemoteId()) != _pDelNodes.end())
            {
                bFind = true;
                break;
            }
        }
        if (!bFind)
            _pDelNodes[pNode->getRemoteId()] = pNode;
    }
}

WDNode::SharedPtr WDBMAuthorityMgr::lockableNode(WDNode::SharedPtr pNode) const
{
    WDNode::SharedPtr pLockableNode = pNode;
    if (_atomicDepth == -1)
        return pLockableNode;

    if (pLockableNode == nullptr)
        return pLockableNode;

    auto depth = pLockableNode->depth();
    while (depth-- > _atomicDepth)
    {
        auto pParent = pLockableNode->parent();
        if (pParent == nullptr)
            break;

        pLockableNode = pParent;
    }

    return pLockableNode;
}
void WDBMAuthorityMgr::recursionLockInsts(WDNode::SharedPtr pParent, int depth, WDNode::Nodes& predInsts, size_t& lockedCnt)
{
    if (pParent == nullptr)
        return ;

    // 判断当前节点是否已被加锁
    if (queryLockNode(pParent->getRemoteId()))
        lockedCnt++;

    predInsts.push_back(pParent);

    if (depth < this->atomicDepth())
    {
        for (const auto& pChild : pParent->children())
        {
            if (pChild == nullptr)
                continue;
            recursionLockInsts(pChild, depth + 1, predInsts, lockedCnt);
        }
    }
}
void WDBMAuthorityMgr::recursionUnlockInsts(WDNode::SharedPtr pParent, int depth, WDNode::Nodes& predInsts, size_t& unlockedCnt)
{
    if (pParent == nullptr)
        return ;

    // 判断当前节点是否已被加锁
    if (!queryLockNode(pParent->getRemoteId()))
        unlockedCnt++;

    predInsts.push_back(pParent);

    if (depth < this->atomicDepth())
    {
        for (const auto& pChild : pParent->children())
        {
            if (pChild == nullptr)
                continue;
            recursionUnlockInsts(pChild, depth + 1, predInsts, unlockedCnt);
        }
    }
}

bool WDBMAuthorityMgr::canEdit(WDNode::SharedPtr pNode)
{
    if (!_enabled)
        return true;

    if (pNode == nullptr)
        return false;

    return pNode->flags().hasFlag(WDNode::Flag::F_Editable);
}

WD_NAMESPACE_END
