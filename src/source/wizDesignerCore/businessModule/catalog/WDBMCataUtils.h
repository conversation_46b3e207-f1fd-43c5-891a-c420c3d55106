#pragma once

#include "../../node/WDNode.h"
#include "../../graphable/WDGraphableInterface.h"

WD_NAMESPACE_BEGIN

/**
* @brief 元件库通用方法, 内部接口
*/
class WD_API WDBMCataUtils
{
public:
    /**
     * @brief 设置关键点数据, 用于场景绘制
     */
    static void SetKeyPoints(WDNode& ptBaseNode, const WDKeyPoints& keyPoints);
    /**
     * @brief 重置关键点数据, 用于场景绘制
     */
    static void ResetKeyPoints(WDNode& ptBaseNode);
    /**
     * @brief 设置PLine数据, 用于场景绘制
     */
    static void SetPLines(WDNode& pLineNode, const WDPLines& pLines);
    /**
     * @brief 重置PLine数据, 用于场景绘制
     */
    static void ResetPLines(WDNode& pLineNode);
    /**
     * @brief 设置型数据, 用于场景绘制
     */
    static void SetGeometries(WDNode& gmBaseNode, const WDGeometries& geoms);
    /**
     * @brief 重置型数据, 用于场景绘制
     */
    static void ResetGeometries(WDNode& gmBaseNode);
};

WD_NAMESPACE_END