#pragma     once

#include    "WDPlatform.hpp"
#include    "WDObject.h"
#include    <atomic>
WD_NAMESPACE_BEGIN

#if WD_PLATFORM == WD_PLATFORM_LINUX
#include  <semaphore.h>
#elif WD_PLATFORM == WD_PLATFORM_APPLE
// Custom semaphore implementation for macOS
typedef struct {
    pthread_mutex_t count_lock;
    pthread_cond_t count_bump;
    unsigned count;
} bosal_sem_t;

// Define sem_t as a pointer type for macOS
typedef void* sem_t;

// Semaphore API compatible with POSIX semaphores
int sem_init(sem_t *psem, int flags, unsigned count);
int sem_destroy(sem_t *psem);
int sem_post(sem_t *psem);
int sem_trywait(sem_t *psem);
int sem_wait(sem_t *psem);
int sem_timedwait(sem_t *psem, const struct timespec *abstim);

#endif

#if WD_PLATFORM == WD_PLATFORM_WIN32

/**
 * @brief 信号量
 */
class WDSemaphore
{
public:
    HANDLE      _semaphore;
public:
    WDSemaphore(long lInit = 1,long lMax = 0x7FFFFFFF)
    {
        _semaphore  =   ::CreateSemaphore(0,lInit,lMax,0);
    }
    virtual ~WDSemaphore()
    {
        if (_semaphore)
        {
            CloseHandle(_semaphore);
            _semaphore  =   nullptr;
        }
    }
    void    reset(long lInit = 0,long lMax = 0x7FFFFFFF)
    {
        _semaphore  =   ::CreateSemaphore(0,lInit,lMax,0);
    }

    virtual void    create(long lInit = 0,long lMax = 0x7FFFFFFF)
    {
        destroy();
        _semaphore  =   ::CreateSemaphore(0,lInit,lMax,0);
    }
    /**
    *   消费信号量
    */
    virtual bool    wait(unsigned long ms = INFINITE)
    {
        switch (WaitForSingleObject(_semaphore, ms))
        {
        case WAIT_OBJECT_0:
            return  true;
        case WAIT_TIMEOUT:
            return  false;
        default:
            return  false;
        }
    }

    /**
    *   释放信号量
    */
    virtual bool    set(long number = 1)
    {
        if (_semaphore == nullptr)
        {
            return  false;
        }
        if(ReleaseSemaphore(_semaphore,number,0) == TRUE)
        {
            return  true;
        }
        else
        {
            return  false;
        }
    }
    /**
    *   销毁
    */
    virtual void    destroy()
    {
        if (_semaphore)
        {
            CloseHandle(_semaphore);
        }
        _semaphore  =   nullptr;
    }

};

#elif WD_PLATFORM == WD_PLATFORM_LINUX || WD_PLATFORM == WD_PLATFORM_APPLE

/**
 * @brief 信号量
 */
class   WDSemaphore
{
public:
    sem_t  _semaphore;
public:
    WDSemaphore(long lInit = 1, long lMax = 0x7FFFFFFF)
    {
        // Initialize with count=lInit, flags=0 (shared between threads in the same process)
        sem_init(&_semaphore, 0, lInit);
    }
    virtual ~WDSemaphore()
    {
        destroy();
    }

    virtual void    create(long lInit = 1, long lMax = 0x7FFFFFFF)
    {
        destroy();
        // Initialize with count=lInit, flags=0 (shared between threads in the same process)
        sem_init(&_semaphore, 0, lInit);
    }
    /**
    *   消费信号量
    */
    virtual bool    wait(unsigned long ms = 0xFFFFFF)
    {
        struct timespec abstime;
        struct timeval  tv;
        gettimeofday(&tv, 0);

        abstime.tv_sec  =   tv.tv_sec + ms / 1000;
        abstime.tv_nsec =   tv.tv_usec*1000 + (ms % 1000)*1000000;
        if (abstime.tv_nsec >= 1000000000)
        {
            abstime.tv_nsec -= 1000000000;
            abstime.tv_sec++;
        }

        return  sem_timedwait(&_semaphore, &abstime) == 0;
    }

    /**
    *   释放信号量
    */
    virtual bool    set(long number = 1)
    {
        bool    res =   true;
        for (long i = 0 ;i < number ; ++ i)
        {
            if(sem_post(&_semaphore) != 0)
            {
                res    =   false;
                break;
            }
        }
        return  res;

    }
    /**
    *   销毁
    */
    virtual void    destroy()
    {
        sem_destroy(&_semaphore);
    }
};
#endif


WD_NAMESPACE_END


