#pragma once

#include "../math/Math.hpp"

WD_NAMESPACE_BEGIN

/**
 * @brief PLine线数据
*/
class WD_API WDPLine: public DPLine
{
private:
    // Pkey    标识符
    std::string _key;
public:
    /**
     * @brief 构造
    */
    inline WDPLine( const std::string& key = ""
        , const DVec3& sPos = DVec3::Zero()
        , const DVec3& sDir = DVec3::AxisZ()
        , const DVec3& ePos = DVec3::Zero())
        :DPLine(sPos, sDir, ePos)
    {
        _key = key;
    }
    /**
     * @brief 构造
    */
    template <typename U>
    inline WDPLine(const std::string& key
        , const TPLine<U>& line)
        : DPLine(line)
    {
        _key = key;
    }    
    /**
     * @brief 析构
    */
    inline ~WDPLine()
    {
    }
public:
    /**
     * @brief 获取 key
    */
    inline const std::string& key() const 
    {
        return _key;
    }
    /**
     * @brief 设置 key
    */
    inline void setKey(const std::string& key)
    {
        _key = key;
    }
    inline void setKey(std::string&& key)
    {
        _key = std::forward<std::string>(key);
    }
};
/**
 * @brief PLine线数据列表
 */
using WDPLines = std::vector<WDPLine>;


WD_NAMESPACE_END

