#pragma once

#include "StandardPrimitiveCommon.h"

WD_NAMESPACE_BEGIN

/**
* @brief 构造圆台体
*/
class WD_API ConeBuilder
{
public:
    /**
    * @brief 生成网格 CONE
    * @param topDiameter 顶面直径
    * @param bottomDiameter 底面直径
    * @param height 高度
    */
    static MeshStruct Mesh(float topDiameter
        , float bottomDiameter
        , float height
        , const MeshLODSelection& lodSelection = MeshLODSelection());
    /**
    * @brief 生成关键点列表 CONE
    * @param topDiameter 顶面直径
    * @param bottomDiameter 底面直径
    * @param height 高度
    */
    static FKeyPoints KeyPoints(float topDiameter, float bottomDiameter, float height);
    /**
    * @brief 生成交点列表 CONE
    * @param topDiameter 顶面直径
    * @param bottomDiameter 底面直径
    * @param height 高度
    */
    static FVec3Vector IntersectPoints(float topDiameter, float bottomDiameter, float height);
    /**
     * @brief 转换 SCON参数 到 CONE参数
     * @param direction 方向
     * @param position 原点位置
     * @param distance 在 direction 方向上，底部圆心点到原点位置的距离
     *   同时也是圆锥的高度
     * @param outTransform 输出的变换信息
     * @param topDiameter 输出的顶面直径
     * @param bottomDiameter 输出的底面直径
     * @param height 输出的高度
     * @return 是否转换成功(当参数无效时,将转换失败)
    */
    static bool SCONToCONE(const FVec3& direction
        , const FVec3& position
        , float distance
        , float diameter
        , FMeshTransform& outTransform
        , float& outTopDiameter
        , float& outBottomDiameter
        , float& outHeight);
    /**
    * @brief 生成网格 CONE
    * @param topDiameter 顶面直径
    * @param bottomDiameter 底面直径
    * @param height 高度
    */
    static MeshStruct SideLines(float topDiameter
        , float bottomDiameter
        , float height
        , const MeshLODSelection& lodSelection = MeshLODSelection());
    /**
    * @brief 生成网格 CONE
    *   简单网格，顶点只包含位置信息，不包含任何法线、UV相关信息，目的是保证顶点位置没有重合
    * @param topDiameter 顶面直径
    * @param bottomDiameter 底面直径
    * @param height 高度
    */
    static MeshStruct SimpleMesh(float topDiameter
        , float bottomDiameter
        , float height
        , const MeshLODSelection& lodSelection = MeshLODSelection());
};

WD_NAMESPACE_END

