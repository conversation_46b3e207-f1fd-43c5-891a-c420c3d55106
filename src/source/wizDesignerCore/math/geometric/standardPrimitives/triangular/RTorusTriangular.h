#ifndef _RTORUSTRIANGULAR_H_
#define _RTORUSTRIANGULAR_H_
#include "Triangular.h"

namespace tri
{
	class CRTorusTriangular :
		public CTriangular
	{
	private:
		FLOAT3D m_R; // 外半径
		FLOAT3D m_r; // 内半径
		FLOAT3D m_h; //高度
		FLOAT3D m_theta; //弧度

		//m_type:
		//0: normal
		//1: m_r = 0.0f
		int m_type;
	public:
		CRTorusTriangular(void);
		~CRTorusTriangular(void);
		//formula : 
		// Ru : [x,y,z] = O + M * [R *cos(alpha), R * sin(alpha), m_h * 0.5f]
		// Rl : [x,y,z] = O + M * [R *cos(alpha), R * sin(alpha), -m_h * 0.5f]
		// ru : [x,y,z] = O + M * [r *cos(alpha), r * sin(alpha), m_h * 0.5f]
		// rl : [x,y,z] = O + M * [r *cos(alpha), r * sin(alpha), -m_h * 0.5f]
		// and here, set O = [0,0,0], M = I. 0<=alpha<=theta 
		virtual bool SetParam(const std::vector<FLOAT3D> &parameters);
		virtual bool Mesh(const std::vector<unsigned int> &segments, bool isClose);
	private:
		bool _DoMesh(int u_seg, int v_seg, bool isClose);
		bool _MeshType1(int u_seg, int v_seg, bool isClose);
		bool _MeshTopDown(int u_seg, int v_seg, FLOAT3D z);
		bool _MeshRectangleType1(int u_seg, int v_seg, bool isClose);
		bool _MeshRectangle(int u_seg);
		bool _MeshUnderSurface(int count);

		FLOAT3D _GetU(FLOAT3D uw)
		{
			return m_theta * uw;
		}
	};
}


#endif
