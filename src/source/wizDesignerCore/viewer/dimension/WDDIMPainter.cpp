#include "WDDIMPainter.h"
#include "../../viewer/primitiveRender/WDTextRender.h"

WD_NAMESPACE_BEGIN

WDDIMPainter::WDDIMPainter()
{
}
WDDIMPainter::~WDDIMPainter()
{
}

DVec2 WDDIMPainter::calcTextsSize(const Texts& texts)
{
    if (texts.empty())
        return DVec2::Zero();

    // 所有文本的宽高
    DVec2 allSz = DVec2::Zero();
    for (const auto& t : texts)
    {
        DVec2 sz = this->calcTextSize(t.first, t.second);
        // 宽度取最大值
        allSz.x = Max(allSz.x, sz.x);
        // 高度累加
        allSz.y += sz.y;
    }

    return allSz;
}

bool WDDIMCollision::addLines(const std::vector<DVec3>& points
    , int groupId
    , const WDDIMLineStyle& style
    , int typeId)
{
    if (points.size() < 2)
        return false;

    for (size_t i = 0; i < points.size() - 1; i += 2)
    {
        if (DVec3::DistanceSq(points[i], points[i + 1]) <= NumLimits<float>::Epsilon)
            continue;
        CSegment segment;
        segment.sPos = points[i];
        segment.ePos = points[i + 1];
        segment.style = style;
        this->addTObject(segment, groupId, typeId);
    }
    return true;
}

bool WDDIMCollision::addBrokenLine(const std::vector<DVec3>& points
    , int groupId
    , const WDDIMLineStyle& style
    , int typeId)
{
    if (points.size() < 2)
        return false;

    for (size_t i = 0; i < points.size() - 1; ++i)
    {
        if (DVec3::DistanceSq(points[i], points[i + 1]) <= NumLimits<float>::Epsilon)
            continue;
        CSegment segment;
        segment.sPos = points[i];
        segment.ePos = points[i + 1];
        segment.style = style;
        this->addTObject(segment, groupId, typeId);
    }
    return true;
}

bool WDDIMCollision::addLoopLine(const std::vector<DVec3>& points
    , int groupId
    , const WDDIMLineStyle& style
    , int typeId)
{
    if (points.size() < 2)
        return false;
    std::vector<DVec3> screenPts;
    screenPts.reserve(points.size());
    for (size_t i = 0; i < points.size(); ++i)
    {
        screenPts.emplace_back(points[i]);
    }
    screenPts.emplace_back(points.front());

    for (size_t i = 0; i < screenPts.size() - 1; ++i)
    {
        if (DVec3::DistanceSq(screenPts[i], screenPts[i + 1]) <= NumLimits<float>::Epsilon)
            continue;
        CSegment segment;
        segment.sPos = screenPts[i];
        segment.ePos = screenPts[i + 1];
        segment.style = style;
        this->addTObject(segment, groupId, typeId);
    }
    return true;
}

bool WDDIMCollision::addRect(const DVec3& center
    , const DVec2& size
    , const DVec3& xAxis
    , const DVec3& planeNormal
    , const WDDIMLineStyle& style
    , int groupId
    , int typeId)
{
    if (size.x <= NumLimits<float>::Epsilon || size.y <= NumLimits<float>::Epsilon)
    {
        assert(false);
        return false;
    }
    if (xAxis.lengthSq() <= NumLimits<float>::Epsilon || planeNormal.lengthSq() <= NumLimits<float>::Epsilon)
    {
        assert(false);
        return false;
    }
    CRect rect;
    rect.center = center;
    rect.planeNormal = planeNormal;
    rect.size = size;
    rect.xAxis = xAxis;
    rect.style = style;
    this->addTObject(rect, groupId, typeId);
    return true;
}

bool WDDIMCollision::addCircle(const DVec3& center
    , const double& redius
    , const DVec3& planeNormal
    , const WDDIMLineStyle& style
    , int groupId
    , int typeId)
{
    //先以正方形给圆做避让
    if (redius <= NumLimits<float>::Epsilon)
    {
        assert(false);
        return false;
    }
    if (planeNormal.lengthSq() <= NumLimits<float>::Epsilon)
    {
        assert(false);
        return false;
    }
    CCircle circle;
    circle.center = center;
    circle.planeNormal = planeNormal;
    circle.radius = redius;
    circle.style = style;
    this->addTObject(circle, groupId, typeId);
    return true;
}

bool WDDIMCollision::checkLines(
    const std::vector<DVec3>& points
    , int groupId
    , const WDDIMLineStyle& style
    , int typeId
    , CItem* pOutItem) const
{
    if (points.size() < 2)
        return false;
    for (size_t i = 0; i < points.size() - 1; i += 2)
    {
        if (DVec3::DistanceSq(points[i], points[i + 1]) <= NumLimits<float>::Epsilon)
            continue;
        CSegment segment;
        segment.sPos = points[i];
        segment.ePos = points[i + 1];
        segment.style = style;
        bool bCheck = this->checkTObject(segment, groupId, typeId, pOutItem);
        if (bCheck)
            return true;
    }
    return false;
}

bool WDDIMCollision::checkBrokenLine(
    const std::vector<DVec3>& points
    , int groupId
    , const WDDIMLineStyle& style
    , int typeId) const
{
    if (points.size() < 2)
        return false;
    for (size_t i = 0; i < points.size() -1; ++i)
    {
        if (DVec3::DistanceSq(points[i], points[i + 1]) <= NumLimits<float>::Epsilon)
            continue;
        CSegment segment;
        segment.sPos = points[i];
        segment.ePos = points[i + 1];
        segment.style = style;
        bool bCheck = this->checkTObject(segment, groupId, typeId);
        if (bCheck)
            return true;
    }
    return false;
}

bool WDDIMCollision::checkLoopLine(
    const std::vector<DVec3>& points
    , int groupId
    , const WDDIMLineStyle& style
    , int typeId) const
{
    if (points.size() < 2)
        return false;
    std::vector<DVec3> screenPts;
    screenPts.reserve(points.size());
    for (size_t i = 0; i < points.size(); ++i)
    {
        screenPts.emplace_back(points[i]);
    }
    screenPts.emplace_back(points.front());

    for (size_t i = 0; i < screenPts.size() - 1; ++i)
    {
        if (DVec3::DistanceSq(screenPts[i], screenPts[i + 1]) <= NumLimits<float>::Epsilon)
            continue;
        CSegment segment;
        segment.sPos = screenPts[i];
        segment.ePos = screenPts[i + 1];
        segment.style = style;
        bool bCheck = this->checkTObject(segment, groupId, typeId);
        if (bCheck)
            return true;
    }
    return false;
}

bool WDDIMCollision::checkRect(const DVec3& center
    , const DVec2& size
    , const DVec3& xAxis
    , const DVec3& planeNormal
    , const WDDIMLineStyle& style
    , int groupId
    , int typeId) const
{
    if (size.x <= NumLimits<float>::Epsilon || size.y <= NumLimits<float>::Epsilon)
    {
        assert(false);
        return false;
    }
    if (xAxis.lengthSq() <= NumLimits<float>::Epsilon || planeNormal.lengthSq() <= NumLimits<float>::Epsilon)
    {
        assert(false);
        return false;
    }
    CRect rect;
    rect.center = center;
    rect.planeNormal = planeNormal;
    rect.size = size;
    rect.xAxis = xAxis;
    rect.style = style;
    return this->checkTObject(rect, groupId, typeId);
}

bool WDDIMCollision::checkCircle(
    const DVec3& center
    , const double& radius
    , const DVec3& planeNormal
    , const WDDIMLineStyle& style
    , int groupId
    , int typeId) const
{
    //先以正方形给圆做避让
    if (radius <= NumLimits<float>::Epsilon )
    {
        assert(false);
        return false;
    }
    if (planeNormal.lengthSq() <= NumLimits<float>::Epsilon)
    {
        assert(false);
        return false;
    }
    CCircle circle;
    circle.center = center;
    circle.radius = radius;
    circle.planeNormal = planeNormal;
    circle.style = style;
    return this->checkTObject(circle, groupId, typeId);
}


WD_NAMESPACE_END
